import CodeAICommHandler from '../commclient/codeAICommHandler';
import { SendMessageRtn } from '../commclient/types/webSocket';
import { WS_APP_GID } from '../common/config';
import { ImportSnippet } from '../codecomplete/types';

import {
  CLIENT_TYPE,
  MessageName,
  PromptRoleType,
  RtnCode,
  SubServiceType,
  ChatMessageType,
  QuestionAskType,
} from '../common/constants';
import {
  getExtensionVersion,
  getCurrentUser,
  getGitRepoUrl,
  getGitRemoteUrls,
  getProjectName,
} from '../common/globalContext';
import { generateUUID } from '../utils/common';
import { Logger } from '../utils/logger';
import { UInfo } from './types/login';
import {
  RequestMessage,
  CodeAIRequestContext,
  CodeAIRequestMessage,
  CodeAIRequestPayload,
  CodeMessage,
  CodeAIRequestPromptChat,
} from './types/codeAI';
import * as vscode from 'vscode';
import { PartialChatRequest } from '../codechat/types';
import { SystemUtils } from '../agent/utils/system';
import { ChangeItem, WorkItem } from '../workItem/types';

export default class CodeAIRequestSender {
  /**
   * 发送注册通道消息
   * @returns
   */
  public static async sendRegisterChannel(): Promise<SendMessageRtn | null> {
    const uInfo = await getCurrentUser();
    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext({ messageName: MessageName.REGISTER_CHANNEL }, uInfo);
    const request = new CodeAIRequestMessage(MessageName.REGISTER_CHANNEL, context).toString();
    Logger.debug(`[CodeAIRequestSender] sendRegisterChannel, ${request}`);

    return CodeAICommHandler.getInstance().registerToCodeAI(request);
  }

  /**
   * 发送获取用户apiKey消息
   * @returns
   */
  public static async sendGetUserApiKey(): Promise<SendMessageRtn | null> {
    const uInfo = await getCurrentUser();
    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext(
      {
        messageName: MessageName.GET_USER_API_KEY,
        reqId: generateUUID(),
      },
      uInfo
    );

    const request = new CodeAIRequestMessage(MessageName.GET_USER_API_KEY, context);
    const payload: CodeAIRequestPayload = {
      clientType: CLIENT_TYPE,
      clientVersion: vscode.version,
      clientPlatform: SystemUtils.getClientPlatform(),
      gitUrls: process.env.ISSEC !== 'false' ? [vscode.workspace.getConfiguration(`通用`).get(`服务器扫描地址`) as string] : await getGitRemoteUrls(),
    };

    request.setPayload(payload);
    Logger.debug(`[CodeAIRequestSender] sendGetUserApiKey, ${request.toString()}`);

    return CodeAICommHandler.getInstance().sendMessageToCodeAI(request.toString());
  }

  /**
   * 发送获取用户apiKey消息
   * @returns
   */
  public static async sendUserActivityNotify(
    activityType: string,
    lines: number,
    count: number,
    isAuto?: boolean,
    latency?: number
  ): Promise<SendMessageRtn | null> {
    const uInfo = await getCurrentUser();
    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext(
      {
        messageName: MessageName.USER_ACTIVITY_NOTIFY,
        reqId: generateUUID(),
      },
      uInfo
    );

    const request = new CodeAIRequestMessage(MessageName.USER_ACTIVITY_NOTIFY, context);
    const payload: CodeAIRequestPayload = {
      client: {
        type: CLIENT_TYPE,
        version: vscode.version,
        pluginVersion: getExtensionVersion(),
        gitUrl: getGitRepoUrl(),
        gitUrls: await getGitRemoteUrls(),
        projectName: getProjectName(),
      },
      activityType,
      lines,
      count,
      isAuto,
      latency,
    };

    request.setPayload(payload);
    Logger.debug(`[CodeAIRequestSender] sendUserActivityNotify, ${request.toString()}`);

    return CodeAICommHandler.getInstance().sendMessageToCodeAI(request.toString());
  }

  /**
   * 发送代码补全请求
   * @param reqId
   * @param fileName
   * @param prefix
   * @param suffix
   * @param language
   * @param maxNewTokens
   * @returns
   */
  public static async sendCodeGenRequest(
    reqId: string,
    fileName: string,
    prefix: string,
    suffix: string,
    language: string,
    maxNewTokens: number,
    stopWords?: string[],
    importSnippets?: ImportSnippet[]
  ) {
    const uInfo = await getCurrentUser();
    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext(
      {
        messageName: MessageName.CODE_GEN,
        reqId,
      },
      uInfo
    );
    const request = new CodeAIRequestMessage(MessageName.CODE_GEN, context);
    const payload: CodeAIRequestPayload = {
      clientType: CLIENT_TYPE,
      clientVersion: vscode.version,
      gitUrls: await getGitRemoteUrls(),
      messages: {
        language,
        filename: fileName,
        prefix,
        suffix,
        max_new_tokens: maxNewTokens,
        stop_words: stopWords,
        importSnippets,
      } as CodeMessage,
    };

    request.setPayload(payload);
    Logger.info(
      `[CodeAIRequestSender] sendCodeGenRequest, reqId: ${reqId}, stop_words: ${JSON.stringify(
        stopWords
      )}, 
      fileName:${fileName}}, importSnippets:${JSON.stringify(importSnippets)}`
    );
    Logger.debug(
      `[CodeAIRequestSender] sendCodeGenRequest, messages: ${JSON.stringify(payload.messages)}`
    );

    return CodeAICommHandler.getInstance().sendMessageToCodeAI(request.toString());
  }

  /**
   * 发送chat请求
   * @param reqId
   * @param question
   * @param prompts
   * @param maxNewTokens
   * @param manualType
   * @returns
   */
  public static async sendChatGenRequest(
    reqId: string,
    question: string,
    prompts: CodeAIRequestPromptChat[],
    maxNewTokens: number,
    manualType?: number,
    extraData?: PartialChatRequest
  ) {
    const { kbId, quote, dialogId, questionAskType, parentReqId, templateId, relatedFiles = [], selectedWorkItems = [] } = extraData || {};
    const uInfo = await getCurrentUser();

    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext(
      {
        messageName: MessageName.CODE_CHAT,
        reqId,
      },
      uInfo
    );

    const request = new CodeAIRequestMessage(MessageName.CODE_CHAT, context);
    const promptChat: CodeAIRequestPromptChat = {
      files: relatedFiles,
      content: question,
      role: PromptRoleType.USER,
      workItems: selectedWorkItems,
    };

    const payload: CodeAIRequestPayload = {
      clientType: CLIENT_TYPE,
      clientVersion: vscode.version,
      gitUrls: await getGitRemoteUrls(),
      messages: {
        max_new_tokens: maxNewTokens,
        sub_service: this.getSubService(manualType),
        prompts: prompts.concat([promptChat]),
        dialogId,
        questionType: questionAskType,
        parentReqId,
        kbId,
        quote,
        modelRouteCondition:
          templateId !== ''
            ? {
              version: 'v1',
              payload: {
                templateId: templateId,
              },
            }
            : undefined,
      } as CodeMessage,
    };

    request.setPayload(payload);
    Logger.info(
      `[CodeAIRequestSender] sendChatGenRequest, reqId: ${request.context.reqId
      }, request:${JSON.stringify(request)}`
    );
    Logger.debug(`[CodeAIRequestSender] sendChatGenRequest, payload: ${JSON.stringify(payload)}`);

    return CodeAICommHandler.getInstance().sendMessageToCodeAI(request.toString());
  }

  /**
   * 发送提交分析请求
   */
  public static async sendCommitChatRequest(
    reqId: string,
    changeList: ChangeItem[],
    workItemList: WorkItem[] | []
  ) {
    const uInfo = await getCurrentUser();

    if (!uInfo.userId || !uInfo.sessionId) {
      return new SendMessageRtn(RtnCode.NOT_LOGIN, '');
    }

    const context = this.getRequestContext(
      {
        messageName: MessageName.COMMIT_CHAT_REQUEST,
        reqId,
      },
      uInfo
    );

    const request = new CodeAIRequestMessage(MessageName.COMMIT_CHAT_REQUEST, context);
    
    const payload: CodeAIRequestPayload = {
      clientType: CLIENT_TYPE,
      clientVersion: vscode.version,
      gitUrls: await getGitRemoteUrls(),
      messages: {
        diffList: changeList,
        workItemList: workItemList.map(item => ({
          title: item.title || '',
          description: item.description || '',
        })),
      },
    };

    request.setPayload(payload);
    Logger.info(`[CodeAIRequestSender] sendCommitChatRequest, reqId: ${reqId}`);
    Logger.debug(`[CodeAIRequestSender] sendCommitChatRequest, payload: ${JSON.stringify(payload)}`);

    return CodeAICommHandler.getInstance().sendMessageToCodeAI(request.toString());
  }

  /**
   * 发送关闭通道消息
   */
  public static async disconnectChannel() {
    CodeAICommHandler.getInstance().disconnect();
  }

  /**
   * 获取context字段
   * @param message
   * @returns
   */
  private static getRequestContext(message: RequestMessage, uInfo: UInfo): CodeAIRequestContext {
    const isRegisterMsg = message.messageName === MessageName.REGISTER_CHANNEL;
    const isApiKeyMsg = message.messageName === MessageName.GET_USER_API_KEY;
    const isUserActivityNotify = message.messageName === MessageName.USER_ACTIVITY_NOTIFY;

    return {
      messageName: message.messageName,
      appGId: isRegisterMsg ? WS_APP_GID : undefined,
      reqId: message.reqId,
      invokerId: uInfo.userId as string,
      // RegisterChannel和GetUserApiKey接口在apikey存在时，新增apiKey字段，去除sessionId字段。
      sessionId:
        (isApiKeyMsg || isRegisterMsg || isUserActivityNotify) && uInfo.apiKey
          ? undefined
          : (uInfo.sessionId as string),
      version: getExtensionVersion(),
      apiKey: uInfo.apiKey,
    };
  }

  /**
   * 获取不同消息的SubService类型
   * @param type
   * @returns
   */
  private static getSubService(type?: ChatMessageType) {
    switch (type) {
      case ChatMessageType.MANUAL_GENERATE:
        return SubServiceType.CODECHAT;
      case ChatMessageType.EXPLAIN:
        return SubServiceType.CODEEXPLAIN;
      case ChatMessageType.COMMENT:
        return SubServiceType.CODECOMMENT;
      case ChatMessageType.UNITTEST:
        return SubServiceType.CODEUNITTEST;
      // TODO: 确认是否要增加sub_service
      case ChatMessageType.OPTIMIZATION:
        return SubServiceType.CODEOPTIMIZATION;
      case ChatMessageType.EXCEPTION_FIX:
        return SubServiceType.CODEEXCEPTIONFIX;
      case ChatMessageType.KB_ASSISTANT:
        return SubServiceType.KBASSISTANT;
      default:
        return SubServiceType.ASSISTANT;
    }
  }
}
