<template>
  <div class="input-box-app-root">
    <MenuOptions :answering="chatStore.answering" />

    <!-- 图标 -->
    <ToolbarIconList />
    <div class="input-box" ref="footerBox">
      <!-- 快捷指令菜单 -->
      <a-dropdown popup-container=".app-footer" position="top" @select="onSelectDropMenu"
        class="footer-dropdown-menu quick-menu">
        <div class="dropdown-menu-div" ref="dropdownMenu"></div>
        <template #content>
          <a-doption v-for="(item, index) in dropMenuOptions" :key="item.label" :value="item.value" :title="item.title">
            <div style="display: flex;justify-content: space-between;">
              <span>
                {{ item.label }}
              </span>
              <span>
                {{ item.title }}
              </span>
            </div>
          </a-doption>
        </template>
      </a-dropdown>
      <div class="input-box-content">
        <RefList ref="refList" :quickKeyword="quickKeyword" @onPick="selectSuggestion"
          @onRefVisibleChange="onRefVisibleChange" @onRemoved="onRemoved" />

      </div>
      <div class="session-input">

        <div id="editable" :contenteditable="!disabledInputBox" ref="messageInput" class="message-input"
          :class="{ disabledBox: disabledInputBox }" @keydown="onEnterKeyDown" @focus="onFocusClick" @input="onInput"
          @blur="onBlur">

        </div>
        <div v-if="!message" class="message-input-placeholder" data-placeholder="请输入您的问题，按Enter键发送">
        </div>
        <a-tooltip content="发送" position="top" mini :arrow-style="{ display: 'none' }">
          <svg-icon name="send" class="send-icon" :style="disabledInputBox ? ({ cursor: 'not-allowed' }) : {}"
            size="13px" @click="submitMessage"></svg-icon>
        </a-tooltip>
      </div>
      <div class="disable-cover" v-if="disabledInputBox">

      </div>
    </div>
  </div>

</template>

<script lang="js" setup>
import { ref, computed, getCurrentInstance, watch, onMounted, readonly } from 'vue';
import RefList from './RefList.vue';
import { useChatStore } from '@/store/chat';
import { useComposer } from '@/store/composer';
import MenuOptions from '@/components/MenuOptions.vue';
import ToolbarIconList from '@/common/components/ToolbarIconList.vue';
import { getTextWithNewlines } from '@/composer/utils.js'
import {
  ChatMessageType,
  ChatMessageTypeDesc,
  ChatMessageTypeEnDesc,
  MaxQuestionLength
} from '@/constants/common';


import { toast } from '@/utils';
const footerBox = ref(null)
const messageInput = ref(null)
const refList = ref(null)
const emit = defineEmits(['onSend', 'shortcut']);

const app = getCurrentInstance();
const $EventBus = app?.appContext.config.globalProperties.$EventBus;

$EventBus.on('setFooterInputMessage', setFooterInputMessage);
const dropdownMenu = ref(null);
const templateId = ref('')

let currentAtPosition = -1;
let isShowingSuggestions = false;
let lastInputWasAt = false;
let ignoreNextSelectionChange = false;
let editable

let lastCursorPosition = null;


const quickKeyword = ref('')

const composer = useComposer()

const chatStore = useChatStore();
const message = ref('');


const dropMenuOptions = readonly(
  [
    {
      title: ChatMessageTypeDesc[ChatMessageType.HELP],
      label: ChatMessageTypeEnDesc[ChatMessageType.HELP],
      value: ChatMessageType.HELP,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.EXPLAIN],
      label: ChatMessageTypeEnDesc[ChatMessageType.EXPLAIN],
      value: ChatMessageType.EXPLAIN,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.UNITTEST],
      label: ChatMessageTypeEnDesc[ChatMessageType.UNITTEST],
      value: ChatMessageType.UNITTEST,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.COMMENT],
      label: ChatMessageTypeEnDesc[ChatMessageType.COMMENT],
      value: ChatMessageType.COMMENT,
    },
    {
      title: ChatMessageTypeDesc[ChatMessageType.OPTIMIZATION],
      label: ChatMessageTypeEnDesc[ChatMessageType.OPTIMIZATION],
      value: ChatMessageType.OPTIMIZATION,
    },

  ]);



const disabledInputBox = computed(() => {
  return !chatStore.codeFreeLoginStatus || chatStore.disabledInputBox || chatStore.answering // || !chatStore.online
})


// 保存光标位置
function saveCursorPosition() {
  const selection = window.getSelection();
  if (!selection.rangeCount) return;

  const range = selection.getRangeAt(0);
  lastCursorPosition = {
    container: range.startContainer,
    offset: range.startOffset
  };
}
function restoreCursorPosition() {
  if (!lastCursorPosition) return null;

  try {
    const range = document.createRange();
    range.setStart(lastCursorPosition.container, lastCursorPosition.offset);
    range.collapse(true);
    return range;
  } catch (e) {
    console.warn("光标位置失效，使用文档末尾");
    return getFallbackRange(); // 降级方案
  }
}


function setFooterInputMessage(item) {
  templateId.value = item.id
  messageInput.value.innerHTML = item.content
  message.value = item.content;
  setInputHeight()
}


// 降级方案：将光标放到文档末尾
function getFallbackRange() {
  const range = document.createRange();
  range.selectNodeContents(editable);
  range.collapse(false); // false 表示移动到末尾
  return range;
}

function setInputHeight() {
  message.value = messageInput.value.textContent
  setTimeout(() => {
    if (messageInput.value) {
      (messageInput.value).style.height = '24px';
      (messageInput.value).style.height = `${(messageInput.value).scrollHeight}px`;
    }
  });

}

/** 代码问答 - 发送信息 */
function submitMessage() {
  if (disabledInputBox.value) return
  if (!chatStore.codeFreeLoginStatus) return toast('未登录或登录失效，请先登录再使用')
  message.value = messageInput.value.textContent
  if (!message.value.trim()) {
    return toast('请输入您的问题，按Enter键提交问题，按Ctrl+Enter键换行');
  }
  sendMessage()

}





/** 代码问答 - 选择快捷指令 */
function onSelectDropMenu(askType) {
  if (templateId.value) {
    chatStore.lastTemplateId = templateId.value
  }
  templateId.value = ''
  emit('shortcut', askType)
}

function sendMessage() {
  if (templateId.value) {
    chatStore.lastTemplateId = templateId.value
  }
  templateId.value = ''
  // 构建 input 和 context
  function parseDivContent(divElement) {
    // 创建一个临时 div 来操作，避免影响原始 DOM
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = divElement.innerHTML;

    // 获取所有 mention spans
    const mentions = tempDiv.querySelectorAll('span.input-mention');

    // 替换 mention 内容
    mentions.forEach(span => {
      const mItem = JSON.parse(span.dataset.item)
      const data = mItem.data
      let mentionText = ''
      if (mItem.itemType === 'project') {
        mentionText = '@codebase'

      } else if (mItem.itemType === 'file') {
        mentionText = data.relativePath || data.path

      } else if (mItem.itemType === 'dir') {
        mentionText = data.relativePath || data.path

      }
      const textNode = document.createTextNode(" " + mentionText + " ");
      span.replaceWith(textNode);
    });

    const contextInputItems = []
    composer.currentSelectedList.forEach(mItem => {
      const data = mItem
      let contextInputItem = {}
      if (mItem.itemType === 'project') {
        contextInputItem = {
          itemTag: "codebase",
          itemValue: ""
        }
      } else if (mItem.itemType === 'file') {
        contextInputItem = {
          itemTag: "file",
          itemValue: data.path
        }
      } else if (mItem.itemType === 'dir') {
        contextInputItem = {
          itemTag: "folder",
          itemValue: data.path
        }
      }
      contextInputItems.push(contextInputItem)
    })

    // 返回纯文本内容
    return {
      input: getTextWithNewlines(tempDiv),
      contextInputItems
    }
  }

  const content = parseDivContent(messageInput.value)
  emit('onSend', content); // @todo
  console.log('emit-onSend', content)

  message.value = '';
  messageInput.value.innerHTML = '';
  setInputHeight()
}
/* 代码问答 - enter */
function onEnterKeyDown(event) {
  if (isShowingSuggestions) {
    if (event.key === 'Escape') {
      hideSuggestions();
    }
    return
  }
  // 检查是否按下了 Shift + Enter 组合键
  if (event.shiftKey && event.key === 'Enter') {
    return
  }
  // 检查是否按下了 Ctrl + Enter 组合键
  else if (event.ctrlKey && event.key === 'Enter') {
    return
  }
  // 检查是否按下了 Command + Enter 组合键（兼容 Mac）
  else if (event.metaKey && event.key === 'Enter') {
    return
  }
  if (event.keyCode == 13 || event.key === '13') {
    event.preventDefault();
    submitMessage();
  }
}

/** 代码问答 - 点击获焦 */
function onFocusClick() {
  chatStore.inputFocusing = true
}

function onRefVisibleChange(v) {
  if (!v) {
    isShowingSuggestions = false
    setTimeout(() => getLastestMentions(), 300)

  }
}

function selectSuggestion(item) {

  const selection = window.getSelection();
  if (!selection.rangeCount) return;

  const range = restoreCursorPosition() ||
    (selection.rangeCount ? selection.getRangeAt(0) : getFallbackRange()); const container = range.startContainer;
  const offset = range.startOffset;

  const atIndex = currentAtPosition >= 0 ? currentAtPosition :
    getTextBeforeCursor(container, offset).lastIndexOf('@');


  // 进入二级菜单
  if (['fileRoot', 'dirRoot', 'libRoot', 'subLibRoot', 'workItemRoot'].includes(item.itemType)) {
    // 如果没有关键词
    if (!quickKeyword.value) return
    // 如果有关键词的时候进去子菜单
    const span = document.createTextNode('')

    // 替换@后的内容
    range.setStart(container, atIndex + 1);
    range.deleteContents();
    range.insertNode(span);

    // 移动光标到mention后
    const newRange = document.createRange();
    newRange.setStartAfter(span);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);
  } else if (item.itemType === 'subLibItem') {
    // 移除@和关键词
    const span = document.createTextNode('')

    // 替换@后的内容
    range.setStart(container, atIndex);
    range.deleteContents();
    range.insertNode(span);

    // 移动光标到mention后
    const newRange = document.createRange();
    newRange.setStartAfter(span);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);

  } else {
    const span = createInputNode(item)

    // 替换@后的内容
    range.setStart(container, atIndex);
    range.deleteContents();
    range.insertNode(span);

    // 移动光标到mention后
    const newRange = document.createRange();
    newRange.setStartAfter(span);
    newRange.collapse(true);
    selection.removeAllRanges();
    selection.addRange(newRange);

    hideSuggestions();
  }

}

function onInput(e) {
  message.value = messageInput.value.textContent

  if (messageInput.value.innerHTML === '/') {
    messageInput.value.innerHTML = ''
    dropdownMenu.value && (dropdownMenu.value).click();
    return
  }
  const inputText = e.data;
  lastInputWasAt = inputText === '@';

  if (lastInputWasAt) {
    ignoreNextSelectionChange = true;
    setTimeout(() => {
      checkForAtSymbol(true);
    }, 30); // 小延迟确保浏览器完成输入处理
  } else {
    checkForAtSymbol(true);
  }
  if (e.inputType === 'deleteContentBackward' || e.inputType === 'deleteContentForward') {
    removeSelectedItem();
  }
  setInputHeight()
  saveCursorPosition()
}
let lastMentions = []
function getLastestMentions() {
  let sameMentions = editable.querySelectorAll(`span.input-mention`);
  lastMentions = [...sameMentions].map(mention => {
    return JSON.parse(mention.dataset.item)
  })
}

function removeSelectedItem() {

  const sameMentions = editable.querySelectorAll(`span.input-mention`);
  const currentMentions = [...sameMentions].map(mention => {
    return JSON.parse(mention.dataset.item)
  })


  const needRemovedList = lastMentions.filter(item => {
    if (currentMentions.find(c => c.itemType === item.itemType && item.data.path === c.data.path)) {
      return false
    }
    return true
  })

  composer.currentSelectedList = composer.currentSelectedList.filter(item => {
    if (needRemovedList.find(c => c.itemType === item.itemType && item.path === c.data.path)) {
      return false
    }
    return true
  })


}

function onRemoved(item) {
  const sameMentions = editable.querySelectorAll(`span.input-mention`);
  for (const mention of sameMentions) {
    const mItem = JSON.parse(mention.dataset.item)
    const data = mItem.data
    if (mItem.itemType === item.itemType && data.path === item.path) {
      mention.remove()
    }
  }
  message.value = messageInput.value.textContent
}

// 检查@触发条件
function checkForAtSymbol(isInputEvent) {
  const selection = window.getSelection();
  if (!selection.rangeCount) return;

  const range = selection.getRangeAt(0);
  const container = range.startContainer;
  const offset = range.startOffset;

  const textBeforeCursor = getTextBeforeCursor(container, offset);

  // 刚输入@时直接触发
  if (isInputEvent && lastInputWasAt) {
    if (/(^|\s)@$/.test(textBeforeCursor)) {
      currentAtPosition = offset - 1;
      showSuggestions('');
      return;
    }
  }

  // 移动光标时检查前面是否有空格
  const match = textBeforeCursor.match(/(^|\s)@([^\s]*)$/);

  if (match) {
    currentAtPosition = textBeforeCursor.lastIndexOf('@');
    const partial = match[2];
    showSuggestions(partial);
  } else {
    hideSuggestions();
  }
}

// 显示建议
function showSuggestions(filter) {
  isShowingSuggestions = true;
  quickKeyword.value = filter
  refList.value.showRefList()

}
function hideSuggestions() {
  isShowingSuggestions = false;
  lastInputWasAt = false;
  ignoreNextSelectionChange = false;
  refList.value.hideRefList()
}


// 获取光标前的文本
function getTextBeforeCursor(container, offset) {
  if (container.nodeType === Node.TEXT_NODE) {
    return container.textContent.substring(0, offset);
  } else if (container.nodeType === Node.ELEMENT_NODE) {
    let text = '';
    for (let i = 0; i < container.childNodes.length; i++) {
      const node = container.childNodes[i];
      if (i < offset) {
        if (node.nodeType === Node.TEXT_NODE) {
          text += node.textContent;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          text += node.textContent;
        }
      }
    }
    return text;
  }
  return '';
}
onMounted(() => {
  editable = document.getElementById('editable');

  editable.addEventListener('paste', (e) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    const selection = window.getSelection();

    if (selection.rangeCount) {
        const range = selection.getRangeAt(0);
        range.deleteContents(); // 删除选中内容
        const textNode = document.createTextNode(text);
        range.insertNode(textNode); // 插入新文本

        // 将光标移动到新插入的文本末尾
        const newRange = document.createRange();
        newRange.setStartAfter(textNode);
        newRange.collapse(true); // 折叠范围到末尾
        selection.removeAllRanges();
        selection.addRange(newRange); // 重新设置光标位置
    }

    setInputHeight()
  });

  // 监听光标移动事件
  document.addEventListener('selectionchange', function () {
    if (chatStore.tab !== 'chat') return

    if (ignoreNextSelectionChange) {
      ignoreNextSelectionChange = false;
      return;
    }
    const selection = window.getSelection();
    if (selection.rangeCount === 0 || !editable.contains(selection.anchorNode)) {
      return;
    }
    checkForAtSymbol(false);
  });
})

function createInputNode(item = {}) {
  const data = item.data
  if(item.itemType === 'project') {
    const dom = document.createElement('span')
    dom.classList.add('input-mention')
    dom.setAttribute('contenteditable', 'false')
    dom.setAttribute('data-item', JSON.stringify(item))
    dom.innerHTML=`&nbsp<span contenteditable="false" class="input-mention__project"><span/>&nbsp`
    return dom
  }
  let label = data.name
  const dom = document.createElement('span')
  dom.classList.add('input-mention')
  dom.setAttribute('contenteditable', 'false')
  dom.setAttribute('data-item', JSON.stringify(item))
  dom.innerHTML = `&nbsp@<span contenteditable="false" style="display: inline-block;max-width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">${label}</span>&nbsp`
  if(item.itemType === 'file') {
    dom.classList.add('input-mention-file')
  }
  return dom
}
function onBlur() {
  chatStore.inputFocusing = false
  setInputHeight();
}

watch(disabledInputBox, (val) => {
  if (!val) {
    setTimeout(() => {
      if (messageInput.value) {
        (messageInput.value).focus()
      }
    })
  }
}, { immediate: true })



</script>

<style lang="scss">
.input-box-app-root {
  position: relative;

  .input-box__diff-file-list {
    position: absolute;
    bottom: calc(100% - 26px);
    /* width: 100%; */
    left: 20px;
    right: 20px;
  }

  .input-box {
    position: relative;
    box-sizing: border-box;
    margin: 0 20px 20px 20px;
    padding: 12px;
    color: var(--vscode-icon-foreground);
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-code-border);

    // overflow: hidden;
    border-radius: 4px;

    &:hover {
      border: 1px solid #307cfb;

      .send-icon {
        color: #307cfb;
      }
    }

    .input-box-content {
      display: flex;
      justify-content: space-between;
    }

    .disabledBox {
      cursor: not-allowed;
    }

    .disable-cover {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: var(--cf-input-disabled-cover-color);
      opacity: 0.4;
      cursor: not-allowed;
    }

    .session-input {
      position: relative;
      border-radius: 4px;
      background-color: var(--vscode-input-background);
      color: var(--vscode-input-foreground);
      border: 1px solid var(--vscode-none-color);
      margin-top: 10px;
      margin-right: -10px;

    }

    .message-input {
      padding-right: 40px;
      height: 24px;
      min-height: 24px;
      width: 100%;
      max-height: 350px;
      overflow-y: auto;
      border: none;
      // border: 1px solid var(--vscode-none-color, transparent);
      color: var(--vscode-inputOption-activeForeground);
      background-color: var(--vscode-input-background);
      resize: none;
      white-space: pre-wrap;

      &:focus {
        outline: none;
        // outline: 1px solid var(--vscode-none-color, transparent);
      }
    }

    .message-input-placeholder {
      position: absolute;
      bottom: 0;
      height: 24px;
      pointer-events: none;
      padding-right: 40px;
      overflow: hidden;
      &::before {
        color: #8D8D8D;
        content: attr(data-placeholder);
        height: 0;
        pointer-events: none;
      }

    }

    .send-icon {
      position: absolute;
      right: 20px;
      bottom: 10px;
      cursor: pointer;
      color: var(--vscode-code-border);
      // color: #307cfb;

    }

    .answering-line {
      position: absolute;
      z-index: 9999;
      top: 0;
      left: 0;
      width: 20px;
      height: 2px;
      background-color: var(--vscode-textLink-activeForeground);
      animation: answeringline infinite 4s ease-in-out;
    }

    @keyframes answeringline {
      0% {
        left: -20px;
      }

      50% {
        width: 40px;
      }

      100% {
        left: calc(100% + 20px);
      }
    }
  }

  .dropdown-menu-div {
    width: 100%;
    position: absolute;
    top: 2px;
  }

  .footer-dropdown-menu {
    width: 100%;
    padding: 0 20px;

    .arco-dropdown {
      box-shadow: 0 4px 10px #0000001a;
      border-radius: 4px;
      background-color: var(--vscode-input-background);
      color: var(--vscode-inputOption-activeForeground);
      border: 1px solid var(--vscode-none-color);
      margin-bottom: 15px;
    }

    &.quick-menu {
      .arco-dropdown {
        margin-bottom: 8px;
      }
    }
  }

  .arco-dropdown-option-content {
    width: 100% !important;
  }
}
</style>
