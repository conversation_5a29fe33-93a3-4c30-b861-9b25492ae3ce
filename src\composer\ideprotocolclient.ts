import { Logger } from "../utils/logger";
import { getCodeAIConfig } from "../common/globalContext";
import { generateIgnoreList } from "../utils/common";

// Types and interfaces
interface Position {
  line: number;
  character: number;
}

interface Range {
  start: Position;
  end: Position;
}

interface RangeInFile {
  filepath: string;
  range: Range;
}

interface RangeInFileWithContents extends RangeInFile {
  contents: string;
}

interface HighlightedCodeUpdate {
  highlightedCode: RangeInFileWithContents[];
  edit: boolean;
}

interface AcceptRejectDiff {
  accepted: boolean;
  stepIndex: number;
}

interface DeleteAtIndex {
  index: number;
}

// Constants
export const DEFAULT_IGNORE_FILETYPES = [
  "*.min.js",
  "*.js.map",
  "*.DS_Store",
  "*-lock.json",
  "*.lock",
  "*.log",
  "*.ttf",
  "*.png",
  "*.jpg",
  "*.jpeg",
  "*.gif",
  "*.mp4",
  "*.svg",
  "*.ico",
  "*.pdf",
  "*.zip",
  "*.gz",
  "*.tar",
  "*.dmg",
  "*.tgz",
  "*.rar",
  "*.7z",
  "*.exe",
  "*.dll",
  "*.obj",
  "*.o",
  "*.o.d",
  "*.a",
  "*.lib",
  "*.so",
  "*.dylib",
  "*.ncb",
  "*.sdf",
  "*.woff",
  "*.woff2",
  "*.eot",
  "*.cur",
  "*.avi",
  "*.mpg",
  "*.mpeg",
  "*.mov",
  "*.mp3",
  "*.mp4",
  "*.mkv",
  "*.webm",
  "*.jar",
  "*.onnx",
  "*.parquet",
  "*.pqt",
  "*.wav",
  "*.webp",
  "*.db",
  "*.sqlite",
  "*.wasm",
  "*.plist",
  "*.profraw",
  "*.gcda",
  "*.gcno",
  "go.sum",
  ".env",
  "app.*.*.js",
  "chunk-*.js",
];

export const DEFAULT_IGNORE_DIRS = [
  // 基础版本控制
  ".git",
  ".svn",
  ".hg",
  ".bzr",
  ".fossil",
  ".repo",
  ".cvs",
  ".cvsignore",

  // IDE和编辑器
  ".vscode",
  ".idea",
  ".vs",
  ".eclipse",
  ".netbeans",
  ".kate-swp",
  ".komodotools",
  ".komodoproject",
  ".visualstudio",
  ".vscodium",
  ".webstorm",
  ".pycharm",
  ".rubymine",
  ".phpstorm",
  ".rider",
  ".goland",
  ".clion",
  ".androidstudio",
  ".xcode",

  // Python相关
  "venv",
  ".venv",
  "env",
  ".env",
  "__pycache__",
  ".pytest_cache",
  ".coverage",
  ".tox",
  ".pytype",
  ".mypy_cache",
  ".pytest_cache",
  ".python-version",
  ".pyenv",
  "pip-wheel-metadata",
  ".ipynb_checkpoints",
  ".spyderproject",
  ".ropeproject",
  ".pyre",
  "node",

  // Node.js相关
  "node_modules",
  ".npm",
  ".yarn",
  ".pnpm",
  ".node-gyp",
  ".node_repl_history",
  ".v8flags",
  ".babel",
  ".next",
  ".nuxt",
  ".gatsby",
  ".remix",
  ".deno",
  ".bun",

  // Java相关
  "target",
  ".m2",
  ".mvn",
  ".gradle",
  "build",
  "out",
  ".settings",
  ".project",
  ".classpath",
  ".factorypath",
  "classes",
  "META-INF",
  "WEB-INF",
  ".springBeans",
  ".sts4-cache",
  ".grails",

  // Ruby相关
  ".bundle",
  "vendor/bundle",
  ".gem",
  ".rvm",
  ".rbenv",
  ".ruby-version",
  ".ruby-gemset",
  "coverage",
  ".solargraph",
  ".byebug_history",

  // Go相关
  "pkg",
  "bin",
  ".gvm",
  ".glide",
  ".dep",
  "vendor",
  ".go-version",
  "testdata",
  ".gonvim",
  ".gore",

  // Rust相关
  "target",
  ".cargo",
  ".rustup",
  ".cargo-cache",
  "Cargo.lock",
  ".rust-version",
  ".rustfmt",
  ".clippy",

  // PHP相关
  "vendor",
  ".composer",
  ".php_cs.cache",
  ".phpunit",
  ".php-version",
  ".phpdoc",
  ".phalcon",
  ".phpbrew",
  ".php-cs-fixer",

  // 前端构建和依赖
  "dist",
  "build",
  ".cache",
  ".parcel-cache",
  ".vite",
  ".rollup",
  ".webpack",
  ".browserify",
  ".esbuild",
  ".swc",
  ".turbo",
  ".storybook",
  ".stylelint",
  ".sass-cache",

  // 移动开发
  ".gradle",
  ".idea",
  "build",
  "captures",
  ".externalNativeBuild",
  ".cxx",
  "*.apk",
  "*.ipa",
  ".pods",
  "Pods",
  "xcuserdata",
  ".playground",
  ".flutter-plugins",
  ".pub-cache",
  ".android",
  ".ios",

  // 容器和云服务
  ".docker",
  ".kubernetes",
  ".helm",
  ".terraform",
  ".vagrant",
  ".chef",
  ".puppet",
  ".ansible",
  ".salt",
  ".aws",
  ".azure",
  ".gcloud",
  ".digitalocean",
  ".heroku",
  ".vercel",
  ".netlify",
  ".firebase",

  // 数据库和缓存
  ".mysql",
  ".postgresql",
  ".mongodb",
  ".redis",
  ".sqlite",
  ".cassandra",
  ".couchdb",
  ".elasticsearch",
  ".neo4j",
  ".influxdb",
  ".clickhouse",
  ".memcached",

  // CI/CD和部署
  ".github",
  ".gitlab",
  ".circleci",
  ".jenkins",
  ".travis",
  ".drone",
  ".teamcity",
  ".bamboo",
  ".buildkite",
  ".appveyor",
  ".codeship",
  ".wercker",
  ".semaphore",
  ".bitrise",

  // 文档和静态站点
  "docs",
  "_site",
  ".jekyll-cache",
  ".hugo_build.lock",
  ".vuepress",
  ".docusaurus",
  ".mkdocs",
  ".sphinx-build",
  ".gitbook",
  ".docz",

  // 工具和实用程序
  ".tmp",
  ".temp",
  ".cache",
  ".local",
  ".config",
  ".history",
  ".log",
  "logs",
  "backup",
  ".backup",
  "archive",
  ".archive",
  ".trash",
  ".recycle",

  // 操作系统特定
  ".DS_Store",
  ".Spotlight-V100",
  ".Trashes",
  ".Trash",
  ".fseventsd",
  "Thumbs.db",
  "Desktop.ini",
  "$RECYCLE.BIN",
  "System Volume Information",

  // 安全和认证
  ".ssh",
  ".gnupg",
  ".gpg",
  ".cert",
  ".credentials",
  ".secrets",
  ".vault",
  ".kube",
  ".keystore",
  ".truststore",

  // 监控和分析
  ".newrelic",
  ".datadog",
  ".sentry",
  ".grafana",
  ".prometheus",
  ".elastic",
  ".splunk",
  ".nagios",
  ".zabbix",

  // 测试和质量保证
  "coverage",
  ".nyc_output",
  ".jest",
  ".cypress",
  ".selenium",
  ".testcafe",
  ".karma",
  ".playwright",
  ".puppeteer",
  ".webdriver",
  ".lighthouse",
  ".axe",
  ".sonar",
  ".codecov",

  // 多媒体和资源
  "assets",
  "media",
  "uploads",
  "downloads",
  "public/uploads",
  "storage/app",
  ".thumbs",
  ".previews",
  ".miniatures",
  ".compressed",

  // 本地化和国际化
  "locale",
  "locales",
  "i18n",
  "translations",
  ".translations",
  ".locale-data",
  ".messages",
  ".intl",

  // 旧文件和备份
  ".old",
  ".bak",
  ".backup",
  ".save",
  ".swp",
  ".swap",
  ".orig",
  ".rej",
  ".previous",
  ".historic",

  // 其他工具和框架
  ".meteor",
  ".sails",
  ".yeoman",
  ".bower",
  ".grunt",
  ".gulp",
  ".brunch",
  ".middleman",
  ".eleventy",
  ".gatsby",
  ".phenomic",
  ".metalsmith",
];

export class IdeProtocolClient {

  private workspacePath: string | null;


  constructor(
    workspacePath: string | null,
  ) {
    this.workspacePath = workspacePath;
    this.initIdeProtocol();
  }

  public handleMessage(text: string, respond: (data: any) => void): void {
    const parsedMessage = JSON.parse(text);
    const messageType = parsedMessage.messageType.toString();
    const data = parsedMessage.data;

    switch (messageType) {
      case 'getGlobalIgnoreList': { 
        const ignoreList = generateIgnoreList();
        respond(ignoreList);
        break;
      }
      case "uniqueId":
        respond({ uniqueId: this.uniqueId() });
        break;
      case "getLastModified":
        try {
          // 获取文件列表
          const fileData = data as { files: string[] };
          const fs = require('fs');
          const pathToLastModified: Record<string, number> = {};

          // 确保 files 数组存在
          if (fileData.files && Array.isArray(fileData.files)) {
            // 获取每个文件的最后修改时间
            for (const file of fileData.files) {
              try {
                const stats = fs.statSync(file);
                pathToLastModified[file] = stats.mtimeMs;
              } catch (error) {
                Logger.error(`[IdeProtocolClient] Error getting last modified time for ${file}:${error}`);
                pathToLastModified[file] = 0; // 文件不存在或无法访问时使用 0
              }
            }
          }

          respond(pathToLastModified);
        } catch (e: any) {
          Logger.error(`[IdeProtocolClient] Error processing getLastModified: ${e.message || e}`);
          respond({});
        }
        break;
      case "getIdeSettings":
        respond({
          remoteConfigServerUrl: "",
          remoteConfigSyncPeriod: "60",
          userToken: ""
        });
        break;

      case "getIdeInfo":
        try {
          // 获取IDE信息 (VS Code环境下)
          const vscode = require('vscode');
          const process = require('process');

          // 获取IDE名称和版本
          const ideName = 'Visual Studio Code';
          const ideVersion = vscode.version || 'Unknown';

          // 检查是否通过SSH连接
          const sshClient = process.env.SSH_CLIENT;
          const sshTty = process.env.SSH_TTY;

          let remoteName = 'local';
          if (sshClient || sshTty) {
            remoteName = 'ssh';
          }

          // 获取扩展版本
          // 在VS Code中，需要通过扩展API获取
          const extensionId = process.env.ISSEC !== 'false' ? 'Secidea.Secidea' : 'srdcloud.srd-copilot-vscode';
          let extensionVersion = 'Unknown';

          // 尝试获取扩展版本
          const extension = vscode.extensions.getExtension(extensionId);
          if (extension) {
            extensionVersion = extension.packageJSON.version;
          }

          respond({
            ideType: "vscode",
            name: ideName,
            version: ideVersion,
            remoteName: remoteName,
            extensionVersion: extensionVersion
          });
        } catch (e: any) {
          Logger.error(`[IdeProtocolClient] Error getting IDE info: ${e.message || e}`);
          // 返回基本信息
          respond({
            ideType: "vscode",
            name: "Visual Studio Code",
            version: "Unknown",
            remoteName: "local",
            extensionVersion: "Unknown"
          });
        }
        break;

      case "getUniqueId":
        respond(this.uniqueId());
        break;

      case "getBranch":
        try {
          // 执行 git 命令获取当前分支名称
          const { execSync } = require('child_process');

          // 设置工作目录
          const workDir = this.workspacePath || '.';

          try {
            // 执行 git 命令获取当前分支名称
            const output = execSync('git rev-parse --abbrev-ref HEAD', {
              cwd: workDir,
              encoding: 'utf8',
              timeout: 5000 // 5秒超时
            }).trim();

            respond(output || "NONE");
          } catch (e: any) {
            // 检查错误信息
            const errorMsg = e.message || '';
            if (errorMsg.includes('git') && errorMsg.includes('not found')) {
              Logger.error("ERROR: Git is not installed or not in PATH");
            } else {
              Logger.error(`ERROR: ${errorMsg}`);
            }
            respond("NONE");
          }
        } catch (e: any) {
          Logger.error(`[IdeProtocolClient] ERROR: ${e.message || e}`);
          respond("NONE");
        }
        break;

      case "getWorkspaceConfigs":
        // 迁移自Kotlin实现
        this.getWorkspaceConfigs().then(configs => {
          respond(configs);
        }).catch(err => {
          Logger.error(`[IdeProtocolClient] Error getting workspace configs:${err}`);
          respond([]);
        });
        break;

      case "copyText":
        // Implementation empty
        break;

      case "readFile":
        try {
          // 从请求中获取文件路径
          const fileData = data as { filepath: string };
          const fs = require('fs');
          const path = require('path');

          // 确保文件路径存在
          if (!fileData.filepath) {
            respond({ error: "No filepath provided" });
            break;
          }

          // 检查文件是否存在
          if (!fs.existsSync(fileData.filepath)) {
            respond(""); // 文件不存在返回空字符串
            break;
          }

          // 读取文件内容
          try {
            // 获取文件大小
            const stats = fs.statSync(fileData.filepath);
            // 限制读取大小，最多读取100000字节
            const sizeToRead = Math.min(100000, stats.size);

            // 读取文件内容
            const buffer = Buffer.alloc(sizeToRead);
            const fd = fs.openSync(fileData.filepath, 'r');
            const bytesRead = fs.readSync(fd, buffer, 0, sizeToRead, 0);
            fs.closeSync(fd);

            if (bytesRead <= 0) {
              respond("");
              break;
            }

            // 将缓冲区转换为UTF-8字符串
            const content = buffer.toString('utf8', 0, bytesRead);
            respond(content);
          } catch (readError: any) {
            Logger.error(`[IdeProtocolClient] Error reading file ${fileData.filepath}:${readError}`);
            respond("");
          }
        } catch (e: any) {
          Logger.error(`[IdeProtocolClient] Error in readFile:${e}`);
          respond("");
        }
        break;

      // ... Continue with other cases following the same pattern
      case "isTelemetryEnabled":
        respond(true);
        break;

      case "readRangeInFile":
        // Implementation empty
        break;
      case "listWorkspaceContents":
        this.listDirectoryContents(null).then(contents => {
          respond(contents);
        }).catch(err => {
          Logger.error(`[IdeProtocolClient] Error listing directory contents:${err}`);
          respond([]);
        });
        break;

      case "getWorkspaceDirs":
        respond(this.workspaceDirectories());
        break;


      case "getTerminalContents":
        respond({ contents: "Terminal cannot be accessed in JetBrains IDE" });
        break;

      case "visibleFiles":
        // Implementation empty
        break;

      case "saveFile":
        // Implementation empty
        break;
      case "fileExists":
        const msgData = data as { filepath: string };
        const fs = require('fs');
        respond(fs.existsSync(msgData.filepath));
        break;
      case "getRepoName":
        try {
          // 执行 git 命令获取仓库名称
          const { execSync } = require('child_process');
          const path = require('path');

          // 设置工作目录
          const workDir = this.workspacePath || '.';

          try {
            // 执行 git 命令获取远程仓库 URL
            const output = execSync('git config --get remote.origin.url', {
              cwd: workDir,
              encoding: 'utf8',
              timeout: 5000 // 5秒超时
            }).trim();

            respond(output || "NONE");
          } catch (e: any) {
            // 检查错误信息
            const errorMsg = e.message || '';
            if (errorMsg.includes('git') && errorMsg.includes('not found')) {
              Logger.error("ERROR: Git is not installed or not in PATH");
            } else {
              Logger.error(`ERROR: ${errorMsg}`);
            }
            respond("NONE");
          }
        } catch (e: any) {
          Logger.error(`[IdeProtocolClient] ERROR: ${e.message || e}`);
          respond("NONE");
        }
        break;
      default:
        Logger.debug(`Unknown messageType:${messageType}`);
        break;
    }
  }

  // Other methods with empty implementations
  public configUpdate(): void {
    // Implementation empty
  }

  private initIdeProtocol(): void {
    // Implementation empty
  }

  public uniqueId(): string {
    try {
      // 使用Node.js的os模块获取网络接口信息
      const os = require('os');
      const networkInterfaces = os.networkInterfaces();

      // 遍历所有网络接口
      for (const interfaceName in networkInterfaces) {
        const interfaces = networkInterfaces[interfaceName];

        // 查找有MAC地址的非内部接口
        for (const iface of interfaces) {
          // 跳过内部接口
          if (!iface.internal) {
            // 格式化MAC地址，与Kotlin实现类似
            const mac = iface.mac.toUpperCase();
            if (mac && mac !== '00:00:00:00:00:00') {
              // 将冒号替换为连字符以匹配Kotlin实现的格式
              return mac.replace(/:/g, '-');
            }
          }
        }
      }

      return "No MAC Address Found";
    } catch (error) {
      Logger.error(`[IdeProtocolClient] Error getting machine unique ID:${error}`);
      return "Error-Getting-Mac-Address";
    }
  }


  // 获取工作区配置文件
  private async getWorkspaceConfigs(): Promise<string[]> {
    try {
      // 获取工作区目录
      const workspaceDirs = this.workspaceDirectories();
      const configs: string[] = [];

      // 遍历每个工作区目录
      for (const workspaceDir of workspaceDirs) {
        // 使用Node.js的fs模块读取目录内容
        const fs = require('fs');
        const path = require('path');

        try {
          // 读取目录内容
          const contents = fs.readdirSync(workspaceDir);

          // 查找.continuerc.json文件
          for (const file of contents) {
            if (file.endsWith('.continuerc.json')) {
              const filePath = path.join(workspaceDir, file);
              // 读取文件内容
              const fileContent = fs.readFileSync(filePath, 'utf8');
              configs.push(fileContent);
            }
          }
        } catch (error) {
          Logger.error(`[IdeProtocolClient] Error reading directory ${workspaceDir}:${error}`);
        }
      }

      return configs;
    } catch (error) {
      Logger.error(`[IdeProtocolClient] Error in getWorkspaceConfigs:${error}`);
      return [];
    }
  }

  // 获取工作区目录列表
  private workspaceDirectories(): string[] {
    // 在实际实现中，这应该返回VSCode工作区的目录
    // 这里只是模拟返回当前工作目录
    if (this.workspacePath) {
      return [this.workspacePath];
    }
    return [];
  }

  // 列出目录内容
  private async listDirectoryContents(directory: string | null): Promise<string[]> {
    try {
      // 获取要遍历的目录列表
      const dirs = directory ? [directory] : this.workspaceDirectories();
      const contents: string[] = [];

      // 遍历每个目录
      for (const dir of dirs) {
        // 递归遍历目录
        await this.traverseDirectory(dir, contents);

        // 限制文件数量
        if (contents.length > 10000) {
          break;
        }
      }

      return contents;
    } catch (error) {
      Logger.error(`[IdeProtocolClient] Error listing directory contents:${error}`);
      return [];
    }
  }

  // 递归遍历目录
  private async traverseDirectory(directory: string, contents: string[]): Promise<void> {
    const fs = require('fs');
    const path = require('path');

    try {
      const entries = fs.readdirSync(directory, { withFileTypes: true });

      for (const entry of entries) {
        const entryName = entry.name;
        const fullPath = path.join(directory, entryName);

        // 如果是目录，则递归处理
        if (entry.isDirectory()) {
          // 检查是否是应该忽略的目录
          if (!this.shouldIgnoreDirectory(entryName)) {
            await this.traverseDirectory(fullPath, contents);
          }
        } else {
          // 如果是文件，则检查是否应该忽略
          if (this.shouldIncludeFile(entryName)) {
            contents.push(fullPath);

            // 限制文件数量
            if (contents.length > 10000) {
              return;
            }
          }
        }
      }
    } catch (error) {
      Logger.error(`[IdeProtocolClient] Error traversing directory ${directory}:${error}`);
    }
  }

  // 检查是否应该忽略目录
  private shouldIgnoreDirectory(dirName: string): boolean {
    return DEFAULT_IGNORE_DIRS.includes(dirName);
  }

  // 检查是否应该包含文件
  private shouldIncludeFile(fileName: string): boolean {
    for (const pattern of DEFAULT_IGNORE_FILETYPES) {
      // 处理glob模式 (例如 *.min.js)
      if (pattern.startsWith('*') && fileName.endsWith(pattern.substring(1))) {
        return false;
      }
      // 处理确切的文件名
      else if (pattern === fileName) {
        return false;
      }
      // 处理特定格式的模式 (例如 app.*.*.js)
      else if (pattern.includes('*') && this.matchesPattern(fileName, pattern)) {
        return false;
      }
    }
    return true;
  }

  // 简单的模式匹配 (用于 app.*.*.js 这样的格式)
  private matchesPattern(fileName: string, pattern: string): boolean {
    // 将模式转换为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*');
    return new RegExp(`^${regexPattern}$`).test(fileName);
  }
}

