

import * as vscode from 'vscode'
import { SrdCommand, VscodeWorkbench } from '../common/constants';
import * as fs from 'fs';
import * as path from 'path';
import { checkNewVersion, downloadNewVersion } from './updateServices';
import { getUpdatedCheckSettings } from '../settings/settingsGetter';


/** 
 * 注册版本信息检测
 * */ 
export async function registerCheckUpdate(context: vscode.ExtensionContext) { 

  // 仅当品牌为Secidea时，才有版本信息检测
  if (process.env.ISSEC !== 'false') {
    
    // 注册命令
    context.subscriptions.push(
      vscode.commands.registerCommand(SrdCommand.CHECK_UPDATE, async () => {
        console.log("[secidea] check update");
        // vscode.window.showInformationMessage('正在检查插件最新版本');

        const packageJsonPath = context.asAbsolutePath(path.join('package.json'));
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath).toString());
        const currentVersion = packageJson.version;

        // 请求
        const response = await checkNewVersion(currentVersion);
        if (response.code != 0) {
          vscode.window.showWarningMessage('插件版本检查更新失败，请检查相关配置项并重试');
          return;
        }

        if (response.hasUpdate) {
          const updateCheckSettings = getUpdatedCheckSettings();

          const saveDir = updateCheckSettings.saveDir;
          if (updateCheckSettings.isAutoDownload) {

            // 如果启用了自动下载更新，则直接下载
            vscode.window.showInformationMessage('正在下载新版本安装包，请稍等...');
            const isDownloadSuccessfully = await downloadNewVersion(saveDir, currentVersion);
            if (isDownloadSuccessfully) {
              vscode.window.showInformationMessage(`新版本插件安装包已下载到：${saveDir}，请手动进行安装`);
            } else {
              vscode.window.showWarningMessage('下载新版本插件失败，请检查相关配置项并重试');
            }
          } else {
            // 显示更新提示对话框
            const selected = await vscode.window.showInformationMessage(`检测到新版本代码助手，是否立即下载新版本安装包？`, '是', '否');
            if (selected === '是') {
              vscode.window.showInformationMessage('正在下载新版本安装包，请稍等...');
              const isDownloadSuccessfully = await downloadNewVersion(updateCheckSettings.saveDir, currentVersion);
              if (isDownloadSuccessfully) {
                vscode.window.showInformationMessage(`新版本插件安装包已下载到：${saveDir}，请手动进行安装`);
              } else {
                vscode.window.showWarningMessage('下载新版本失败，请检查相关配置项并重试');
              }
            }
          }
        } else {
          vscode.window.showInformationMessage('已是最新版本');
        }
      })
    )
  }
}
