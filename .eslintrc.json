{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "plugins": ["@typescript-eslint", "prettier"], "ignorePatterns": ["out", "dist", "**/*.d.ts", "package.json", "yarn.lock", "*.config.js", "l10n", "webview/**/*"], "rules": {"prettier/prettier": "error", "arrow-body-style": "off", "prefer-arrow-callback": "off", "array-callback-return": ["warn", {"allowImplicit": true}], "block-spacing": "error", "no-var": "warn", "no-new-func": "warn", "no-useless-constructor": "warn", "no-duplicate-imports": "warn", "no-useless-rename": ["error", {"ignoreDestructuring": true, "ignoreImport": false, "ignoreExport": false}], "eqeqeq": ["error", "always", {"null": "ignore"}], "no-nested-ternary": "warn", "no-template-curly-in-string": "warn", "no-empty-function": "off", "lines-between-class-members": ["error", "always"]}, "overrides": [{"files": ["*.ts", "*.mts", "*.cts", "*.tsx"], "rules": {"@typescript-eslint/member-delimiter-style": "error", "padded-blocks": ["error", "never"], "quotes": "off", "@typescript-eslint/quotes": ["warn", "single", {"allowTemplateLiterals": true}], "curly": ["error", "all"], "@typescript-eslint/type-annotation-spacing": "error", "@typescript-eslint/triple-slash-reference": "error", "@typescript-eslint/explicit-member-accessibility": "warn", "@typescript-eslint/member-ordering": "warn", "@typescript-eslint/consistent-type-definitions": "error", "@typescript-eslint/no-empty-interface": "warn", "@typescript-eslint/no-invalid-void-type": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/consistent-type-assertions": "error", "@typescript-eslint/no-namespace": "error", "prefer-const": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-empty-function": "off"}}, {"files": ["*.js", "*.jsx", "*.mjs"], "rules": {"no-prototype-builtins": "warn", "prefer-object-spread": "warn", "wrap-iife": ["error", "inside"], "brace-style": ["error", "1tbs"], "multiline-comment-style": ["warn", "starred-block"], "spaced-comment": ["error", "always"], "comma-style": ["error", "last"], "arrow-parens": ["error", "as-needed"], "@typescript-eslint/no-var-requires": "off"}}]}