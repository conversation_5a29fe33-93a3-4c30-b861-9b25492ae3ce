<!-- 知识库 -->
<template>
  <div style="position: relative;" class="lib-select">
    <a-dropdown ref="dropdownRef" popup-container=".composer-input-box-root" :hide-on-select="false" position="tl"
      @popup-visible-change="onPopupVisibleChange" class="footer-dropdown-menu">
      <div class="add-button" ref="addButtonRef">
        <span class="add-icon">
          <icon-plus class="button-icon" />
        </span>
        <template v-if="hasSelected">
          <a-button class="add-button-item" v-for="(item, index) in currentSelectedList" size="mini"
            @click.stop="openFile(item)">
            <svg-icon :name="item.icon" style="margin-right: 5px;"></svg-icon>
            <span
              style="display: inline-block;max-width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
              {{ item.name }}
            </span>
            <template v-if="getShowLineNumber(item)">
              ({{ item.startLine + 1 }} - {{ item.endLine + 1 }})
            </template>
            <icon-close style="margin-left: 10px;" @click.stop="removeSelectedItem(index)" />
          </a-button>
        </template>


        <span v-if="!hasSelected" style="user-select: none;opacity: 0.7;">
          添加参考
        </span>
      </div>

      <template #content>
        <div class="option-item-list-container">
          <!-- 来自于@ -->
          <template v-if="isShowByAt">
            <div v-if="!showByAtAllList.length" style="padding: 10px 12px;">
              没有找到相关内容
            </div>

            <template v-if="quickKeyword">
              <div v-for="(item, index) in showByAtAllList" :key="item.name + index">
                <!-- main list -->
                <a-doption class="main-list-bar" :class="{ 'composer-actived-mf': index === showByAtIndex }"
                  v-if="item.from === 'mainList'" :key="item.label" :value="item.value"
                  @click.native="onClickItem(item)">
                  <a-tooltip :content="item.title || item.label" position="top">
                    <div style="display: flex;justify-content: space-between;">
                      <span style="display: flex; align-items: center;">
                        <svg-icon :name="item.icon"
                          style="color: currentColor;opacity:0.6;margin-right: 5px;"></svg-icon>
                        {{ item.label }}
                      </span>

                      <template v-if="!['currentFile', 'currentProject'].includes(item.value)">
                        <span class="main-list-item-icon">
                          <icon-right />
                        </span>
                        <span class="main-list-item-tips">
                          点击后选择
                        </span>
                      </template>
                      <template v-if="item.value === 'currentFile'">
                        <span v-if="currentFilePath" class="main-list-item-tips">
                          点击后选择
                        </span>
                        <span v-else class="main-list-item-tips-show">
                          未打开文件
                        </span>
                      </template>
                      <template v-if="item.value === 'currentProject'">
                        <span v-if="hasProject" class="main-list-item-tips">
                          点击后选择
                        </span>
                        <span v-else class="main-list-item-tips-show">
                          未打开工程
                        </span>
                      </template>
                    </div>
                  </a-tooltip>
                </a-doption>
                <!-- file list -->
                <a-doption class="lib-item-bar" :class="{ 'composer-actived-mf': index === showByAtIndex }"
                  v-else-if="item.from === 'fileList'" :key="item.path" :value="item.value"
                  @click.native="onClickItem(item)">
                  <a-tooltip :content="item.path" position="top">
                    <div style="display: flex;justify-content: space-between;">
                      <span class="longlong-filename">
                        <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>

                        {{ item.label }}
                      </span>
                      <span class="list-item-sub-current" v-if="item.path === currentFilePath">
                        当前文件
                      </span>
                      <span v-else class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                        {{ item.title }}
                      </span>
                      <span class="lib-item-sub-tips">
                        点击后选择
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
                <!-- dir list -->
                <a-doption class="lib-item-bar" :class="{ 'composer-actived-mf': index === showByAtIndex }"
                  v-else-if="item.from === 'dirList'" :value="item.value" @click.native="onClickItem(item)">
                  <a-tooltip :content="item.path" position="top">
                    <div style="display: flex;justify-content: space-between;">
                      <span class="longlong-filename">
                        <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>
                        {{ item.label }}
                      </span>

                      <span class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                        {{ item.title }}
                      </span>
                      <span class="lib-item-sub-tips">
                        点击后选择
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
                <a-doption v-else-if="item.from === 'workItemList'" class="lib-item-bar"
                  :class="{ 'actived-mf': index === showByAtIndex }" :key="item.value" :value="item.value"
                  @click.native="onClickItem(item)">
                  <a-tooltip :content="item.title" position="top">
                    <div style="display: flex;justify-content: space-between;gap: 10px">
                      <span class="longlong-filename">
                        {{ item.workItemKey }}
                      </span>
                      <span
                        style="opacity: 0.6;font-size: 12px; flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        {{ item.title }}
                      </span>
                      <span style="margin-right: 20px;opacity: 0.6;">
                        {{ item.creatorDisplayName }}
                      </span>
                      <span @click.stop="onClickLink(item)">
                        <svg-icon name="icon-link" style="margin-right: 8px;"></svg-icon>
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
              </div>

            </template>
            <template v-else>
              <!-- main list -->
              <div v-if="listType === 'main'">
                <a-doption class="main-list-bar" v-for="(item, index) in mainList"
                  :class="{ 'composer-actived-mf': index === showByAtIndex }" :key="item.label" :value="item.value"
                  @click.native="onClickItem(item)">

                  <div style="display: flex;justify-content: space-between;">
                    <span style="display: flex; align-items: center;">
                      <svg-icon :name="item.icon" style="color: currentColor;opacity:0.6;margin-right: 5px;"></svg-icon>
                      {{ item.label }}
                    </span>

                    <template v-if="!['currentFile', 'currentProject'].includes(item.value)">
                      <span class="main-list-item-icon">
                        <icon-right />
                      </span>
                      <span class="main-list-item-tips">
                        点击后选择
                      </span>
                    </template>
                    <template v-if="item.value === 'currentFile'">
                      <span v-if="currentFilePath" class="main-list-item-tips">
                        点击后选择
                      </span>
                      <span v-else class="main-list-item-tips-show">
                        未打开文件
                      </span>
                    </template>
                    <template v-if="item.value === 'currentProject'">
                      <span v-if="hasProject" class="main-list-item-tips">
                        点击后选择
                      </span>
                      <span v-else class="main-list-item-tips-show">
                        未打开工程
                      </span>
                    </template>
                  </div>
                </a-doption>
              </div>
              <div v-if="listType === 'file'">
                <a-doption value="back" :class="{ 'composer-actived-mf': index === showByAtIndex }" class="sub-back-bar"
                  @click.native="onClickBack">
                  <div style="display: flex;justify-content: space-between;align-items: center;">
                    <span style="display: flex;align-items: center;">
                      <icon-left style="margin-right: 8px;" />
                      <svg-icon name="icon-file" style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                      文件
                    </span>
                    <span class="sub-back-text">
                      点击后返回
                    </span>
                  </div>
                </a-doption>
                <div v-if="fileList.length === 0" style="padding: 10px 12px;">
                  <span v-if="searchKeyword">
                    未搜索到相关文件
                  </span>
                  <span v-else>
                    没有打开的文件
                  </span>
                </div>
                <template v-else>
                  <a-doption class="lib-item-bar" :class="{ 'composer-actived-mf': index === showByAtIndex }"
                    v-for="(item, index) in fileList" :key="item.path" :value="item.value"
                    @click.native="onClickItem(item)">
                    <a-tooltip :content="item.path" position="top">
                      <div style="display: flex;justify-content: space-between;">
                        <span class="longlong-filename">
                          <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>

                          {{ item.label }}
                        </span>
                        <span class="list-item-sub-current" v-if="item.path === currentFilePath">
                          当前文件
                        </span>
                        <span v-else class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                          {{ item.title }}
                        </span>
                        <span class="lib-item-sub-tips">
                          点击后选择
                        </span>
                      </div>
                    </a-tooltip>
                  </a-doption>
                </template>

              </div>
              <div v-if="listType === 'dir'">
                <a-doption value="back" class="sub-back-bar" @click.native="onClickBack">
                  <div style="display: flex;justify-content: space-between;align-items: center;">
                    <span style="display: flex;align-items: center;">
                      <icon-left style="margin-right: 8px;" />
                      <svg-icon name="icon-folder-new"
                        style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                      目录
                    </span>
                    <span class="sub-back-text">
                      点击后返回
                    </span>
                  </div>
                </a-doption>
                <div v-if="dirList.length === 0" style="padding: 10px 12px;">
                  <span v-if="searchKeyword">
                    未搜索到相关目录
                  </span>
                  <span v-else>
                    没有目录
                  </span>
                </div>
                <template v-else>
                  <a-doption class="lib-item-bar" :class="{ 'composer-actived-mf': index === showByAtIndex }"
                    v-for="(item, index) in dirList" :key="item.path" :value="item.value"
                    @click.native="onClickItem(item)">
                    <a-tooltip :content="item.path" position="top">
                      <div style="display: flex;justify-content: space-between;">
                        <span class="longlong-filename">
                          <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>
                          {{ item.label }}
                        </span>

                        <span class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                          {{ item.title }}
                        </span>
                        <span class="lib-item-sub-tips">
                          点击后选择
                        </span>
                      </div>
                    </a-tooltip>
                  </a-doption>
                </template>

              </div>
              <div v-if="listType === 'workItem'">
                <a-doption value="back" class="sub-back-bar" @click.native="onClickBack">
                  <div style="display: flex;justify-content: space-between;align-items: center;">
                    <span style="display: flex;align-items: center;">
                      <icon-left style="margin-right: 8px;" />
                      <svg-icon name="icon-lib" style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                      工作项
                    </span>
                    <span class="sub-back-text">
                      点击后返回
                    </span>
                  </div>
                </a-doption>
                <div v-if="workItemList.length === 0" style="padding: 10px 12px;">
                  {{ chatStore.workItemError || '未搜索到相关工作项' }}
                </div>
                <template v-else>
                  <a-doption class="lib-item-bar" :class="{ 'actived-mf': index === showByAtIndex }"
                    v-for="(item, index) in workItemList" :key="item.value" :value="item.value"
                    @click.native="onClickItem(item)">
                    <a-tooltip :content="item.title" position="top">
                      <div style="display: flex;justify-content: space-between;gap: 10px">
                        <span class="longlong-filename">
                          {{ item.workItemKey }}
                        </span>
                        <span
                          style="opacity: 0.6;font-size: 12px; flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                          {{ item.title }}
                        </span>
                        <span style="margin-right: 20px;opacity: 0.6;">
                          {{ item.creatorDisplayName }}
                        </span>
                        <span @click.stop="onClickLink(item)">
                          <svg-icon name="icon-link" style="margin-right: 8px;"></svg-icon>
                        </span>
                      </div>
                    </a-tooltip>
                  </a-doption>
                </template>

              </div>
            </template>

          </template>
          <!-- 来自正常唤起 -->
          <template v-else>
            <div v-if="listType === 'main'">
              <a-doption class="main-list-bar" v-for="(item, index) in mainList" :key="item.label" :value="item.value"
                @click.native="onClickItem(item)">
                <div style="display: flex;justify-content: space-between;">
                  <span style="display: flex; align-items: center;">
                    <svg-icon :name="item.icon" style="color: currentColor;opacity:0.6;margin-right: 5px;"></svg-icon>
                    {{ item.label }}
                  </span>

                  <template v-if="!['currentFile', 'currentProject'].includes(item.value)">
                    <span class="main-list-item-icon">
                      <icon-right />
                    </span>
                    <span class="main-list-item-tips">
                      点击后选择
                    </span>
                  </template>
                  <template v-if="item.value === 'currentFile'">
                    <span v-if="currentFilePath" class="main-list-item-tips">
                      点击后选择
                    </span>
                    <span v-else class="main-list-item-tips-show">
                      未打开文件
                    </span>
                  </template>
                  <template v-if="item.value === 'currentProject'">
                    <span v-if="hasProject" class="main-list-item-tips">
                      点击后选择
                    </span>
                    <span v-else class="main-list-item-tips-show">
                      未打开工程
                    </span>
                  </template>
                </div>
              </a-doption>

            </div>

            <div v-if="listType === 'file'">
              <a-doption value="back" class="sub-back-bar" @click.native="onClickBack">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                  <span style="display: flex;align-items: center;">
                    <icon-left style="margin-right: 8px;" />
                    <svg-icon name="icon-file" style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                    文件
                  </span>
                  <span class="sub-back-text">
                    点击后返回
                  </span>
                </div>
              </a-doption>
              <div v-if="fileList.length === 0" style="padding: 10px 12px;">
                <span v-if="searchKeyword">
                  未搜索到相关文件
                </span>
                <span v-else>
                  没有打开的文件
                </span>
              </div>
              <template v-else>
                <a-doption class="lib-item-bar" v-for="(item, index) in fileList" :key="item.path" :value="item.value"
                  @click.native="onClickItem(item)">
                  <a-tooltip :content="item.path" position="top">
                    <div style="display: flex;justify-content: space-between;">
                      <span class="longlong-filename">
                        <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>

                        {{ item.label }}
                      </span>
                      <span class="list-item-sub-current" v-if="item.path === currentFilePath">
                        当前文件
                      </span>
                      <span v-else class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                        {{ item.title }}
                      </span>
                      <span class="lib-item-sub-tips">
                        点击后选择
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
              </template>

            </div>
            <div v-if="listType === 'dir'">
              <a-doption value="back" class="sub-back-bar" @click.native="onClickBack">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                  <span style="display: flex;align-items: center;">
                    <icon-left style="margin-right: 8px;" />
                    <svg-icon name="icon-folder-new"
                      style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                    目录
                  </span>
                  <span class="sub-back-text">
                    点击后返回
                  </span>
                </div>
              </a-doption>
              <div v-if="dirList.length === 0" style="padding: 10px 12px;">
                <span v-if="searchKeyword">
                  未搜索到相关目录
                </span>
                <span v-else>
                  没有目录
                </span>
              </div>
              <template v-else>
                <a-doption class="lib-item-bar" v-for="(item, index) in dirList" :key="item.path" :value="item.value"
                  @click.native="onClickItem(item)">
                  <a-tooltip :content="item.path" position="top">
                    <div style="display: flex;justify-content: space-between;">
                      <span class="longlong-filename">
                        <svg-icon :name="getFileIcon(item)" style="margin-right: 8px;"></svg-icon>
                        {{ item.label }}
                      </span>

                      <span class="lib-item-sub-text" style="opacity: 0.6;font-size: 12px">
                        {{ item.title }}
                      </span>
                      <span class="lib-item-sub-tips">
                        点击后选择
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
              </template>

            </div>
            <div v-if="listType === 'workItem'">
              <a-doption value="back" class="sub-back-bar" @click.native="onClickBack">
                <div style="display: flex;justify-content: space-between;align-items: center;">
                  <span style="display: flex;align-items: center;">
                    <icon-left style="margin-right: 8px;" />
                    <svg-icon name="icon-workitem"
                      style="margin-right: 8px;opacity: 0.6;color: currentColor;"></svg-icon>
                    工作项
                  </span>
                  <span class="sub-back-text">
                    点击后返回
                  </span>
                </div>
              </a-doption>
              <div v-if="workItemList.length === 0" style="padding: 10px 12px;">
                {{ chatStore.workItemError || '未搜索到相关工作项' }}
              </div>
              <template v-else>
                <a-doption class="lib-item-bar" v-for="(item, index) in workItemList" :key="item.value"
                  :value="item.value" @click.native="onClickItem(item)">
                  <a-tooltip :content="item.title" position="top">
                    <div style="display: flex;justify-content: space-between;gap: 10px">
                      <span class="longlong-filename">
                        {{ item.workItemKey }}
                      </span>
                      <span
                        style="opacity: 0.6;font-size: 12px; flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        {{ item.title }}
                      </span>
                      <span style="margin-right: 20px;opacity: 0.6;">
                        {{ item.creatorDisplayName }}
                      </span>
                      <span @click.stop="onClickLink(item)">
                        <svg-icon name="icon-link" style="margin-right: 8px;"></svg-icon>
                      </span>
                    </div>
                  </a-tooltip>
                </a-doption>
              </template>

            </div>
          </template>
        </div>
      </template>
      <template v-if="!isShowByAt && ['file', 'dir', 'workItem'].includes(listType)" #footer>
        <div class="dropdown-footer-search-bar">
          <a-input-search ref="searchInput" :style="{ width: '100%' }" placeholder="请输入关键字搜索" v-model="searchKeyword" />
        </div>
      </template>
    </a-dropdown>

  </div>
</template>

<script setup>
import { IconPlus } from '@arco-design/web-vue/es/icon';
import { computed, onMounted, ref, getCurrentInstance, defineExpose, defineEmits } from 'vue'
import { useChatStore } from '@/store/chat';
import * as sender from '@/MessageSender'
import { ChatMessageType, WebViewReqCommand } from '@/constants/common'
import { FileTypeIcon } from '@/utils';
import { Message } from '@arco-design/web-vue';
import { useComposer } from '@/store/composer'
import { flattenNestedArrayBFS, isElementVisible, calculateScrollPosition } from './utils.js'
const emit = defineEmits(['onPick', 'onRefVisibleChange', 'onRemoved'])
const props = defineProps({
  quickKeyword: {
    type: String,
    default: ''
  }
})

const app = getCurrentInstance();
const composer = useComposer()
// 是否由@唤起
const isShowByAt = ref(false)
const searchInput = ref(null)

// 由@唤起的item下标
const showByAtIndex = ref(0)

const $EventBus = app?.appContext.config.globalProperties.$EventBus;

const chatStore = useChatStore()
const dropdownRef = ref(null)
const addButtonRef = ref(null)
let searchKeyword = ref('')
let listType = ref('main') //  main dir file
const mainList = computed(() => {
  const options = [
    {
      icon: 'icon-current-file',
      label: '当前文件',
      name: '当前文件',
      value: 'currentFile',
    },
    {
      icon: 'codebase-icon',
      label: '当前工程',
      name: 'codebase',
      value: 'currentProject'
    },
    {
      icon: 'icon-file',
      label: '文件',
      name: '文件',
      value: 'file',
      hasSub: true
    },
    {
      icon: 'icon-folder-new',
      label: '目录',
      name: '目录',
      value: 'dir',
      hasSub: true
    },
    {
      icon: 'icon-workitem',
      label: '工作项',
      name: '工作项',
      value: 'workItem',
      hasSub: true
    },
  ].map(item => {
    return {
      ...item,
      from: 'mainList'
    }
  })
  return options.filter(item => item.name.includes(searchKeyword.value.toLowerCase()))
})


const currentFilePath = computed(() => {
  const lastChangedFile = composer.recentlyUsedFiles.find(item => item.isCurrentFile)

  if (lastChangedFile) {
    return lastChangedFile.path
  }
  return ''
})

const hasProject = computed(() => {
  return composer.fileTree && composer.fileTree.length
})

const allFileList = computed(() => {
  return flattenNestedArrayBFS(composer.fileTree).map(item => {

    let title = item.path
    if (item.path.length > 40) {
      title = '...' + item.path.slice(-40)
    }

    return {
      ...item,
      label: item.name,
      value: item.path,
      title,
      from: 'fileList'
    }
  })
})


const dirList = computed(() => {
  return composer.folderList.map(item => {
    let title = item.path
    if (item.path.length > 40) {
      title = '...' + item.path.slice(-40)
    }
    return {
      ...item,
      label: item.name,
      value: item.path,
      title,
      from: 'dirList',
      icon: FileTypeIcon.folder
    }
  }).filter(item => item.label.toLowerCase().includes(searchKeyword.value.toLowerCase()))
})


const workItemList = computed(() => {
  /*
  {
    "id": "testleiy-4280",
    "workItemKey": "testleiy-4280",
    "description": "",
    "title": "测试工作项2",
    "creatorDisplayName": "刘德华",
    "url": "/zxwim/null/allWorkItems/testleiy-4280"
  }
  */
  return chatStore.workItems.map(item => {
    return {
      ...item,
      name: [item.title, item.workItemKey, item.description].join('_'),
      label: item.title,
      value: item.id,
      title: item.title,
      from: 'workItemList',
      icon: FileTypeIcon.workItem
    }
  }).filter(item => item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()))

})

const fileList = computed(() => {
  if (!searchKeyword.value && !props.quickKeyword) {
    const recentList = composer.recentlyUsedFiles.slice(0, 20).map(item => {
      let title = item.path
      if (item.path.length > 40) {
        title = '...' + item.path.slice(-40)
      }
      return {
        ...item,
        label: item.name,
        value: item.path,
        title,
        from: 'fileList'
      }
    })
    let i = 0
    for (const file of allFileList.value) {
      if (i > 200) break
      if (recentList.find(item => item.path === file.path)) {
        continue
      } else {
        recentList.push(file)
        i++
      }
    }
    return recentList
  }
  return allFileList.value.filter(item => item.label.toLowerCase().includes(searchKeyword.value.toLowerCase()))
})


const currentSelectedList = computed(() => {
  return composer.currentSelectedList.map(item => {
    const fullName = item.name || item.path || item.value
    const type = fullName.split('.')[fullName.split('.').length - 1]
    return {
      ...item,
      icon: item.icon || FileTypeIcon[type] || FileTypeIcon.file
    }
  })
})
// 如果@之后带有文本，全部显示过滤
const showByAtAllList = computed(() => {
  const main = mainList.value
  return [].concat(main, fileList.value, dirList.value, workItemList.value).filter(item => {
    return item.name.toLowerCase().includes(props.quickKeyword.toLowerCase())
  })

})

const hasSelected = computed(() => {
  return !!composer.currentSelectedList.length

})

function getShowLineNumber(item) {
  return item.type === ChatMessageType.QA_RELATED_FILES && typeof item.startLine === 'number' && typeof item.endLine === 'number' && item.startLine >= 0 && item.startLine >= 0
}

function getFileIcon(file) {
  const paths = file.path.split('.')
  const type = paths[paths.length - 1]
  return file.icon || FileTypeIcon[type] || FileTypeIcon.file
}
function openFile(file) {
  chatStore.openFile(file)
}
function focusSearchInput(){
  setTimeout(()=> {
    searchInput.value.focus()
  }, 300)
}

function removeSelectedItem(index) {
  const item = composer.currentSelectedList[index]
  emit('onRemoved', item)
  composer.currentSelectedList = composer.currentSelectedList.filter((_, x) => index !== x)


}

// 这里很复杂哦
function onClickItem(item) {
  console.log('onClickItem', isShowByAt.value)
  // 如果是@唤起
  if (isShowByAt.value) {
    if (item.from === 'mainList') {
      if (item.value === 'currentFile') {
        // 选择当前文件
        const lastChangedFile = composer.recentlyUsedFiles.find(item => item.isCurrentFile)
        onClickFileItem(lastChangedFile, true)

      } else if (item.value === 'file') {
        // 进入文件列表
        listType.value = 'file'
        emit('onPick', { itemType: 'fileRoot' })
        showByAtIndex.value = 0
      } else if (item.value === 'dir') {
        // 进入目录列表
        listType.value = 'dir'
        emit('onPick', { itemType: 'dirRoot' })
        showByAtIndex.value = 0
      } else if (item.value === 'currentProject') {
        // 当前代码库
        onClickProject(item, true)
      } else if (item.value === 'workItem') {
        listType.value = 'workItem'
        showByAtIndex.value = 0
        emit('onPick', { itemType: 'workItemRoot' })
      }
    } else if (item.from === 'fileList') {
      // 来自具体文件
      onClickFileItem(item, true)
    } else if (item.from === 'dirList') {
      // 来自具体目录
      onClickDirItem(item, true)
    } else if (item.from === 'workItemList') {
      onClickWorkItem(item, true)
    }
  } else {
    // 如果是正常唤起
    if (item.from === 'mainList') {
      if (item.value === 'currentFile') {
        // 选择当前文件
        const lastChangedFile = composer.recentlyUsedFiles.find(item => item.isCurrentFile)
        onClickFileItem(lastChangedFile)

      } else if (item.value === 'file') {
        // 进入文件列表
        listType.value = 'file'
        focusSearchInput()
      } else if (item.value === 'dir') {
        // 进入目录列表
        listType.value = 'dir'
        focusSearchInput()

      } else if (item.value === 'currentProject') {
        // 当前代码库
        onClickProject(item)
      } else if (item.value === 'workItem') {
        listType.value = 'workItem'
        focusSearchInput()
      }
    } else if (item.from === 'fileList') {
      // 来自具体文件
      onClickFileItem(item)
    } else if (item.from === 'dirList') {
      // 来自具体目录
      onClickDirItem(item)
    } else if (item.from === 'workItemList') {
      onClickWorkItem(item)
    }
  }

}

function onClickProject(item, byAt) {
  dropdownRef.value.handlePopupVisibleChange(false)

  if (!hasProject.value) return

  if (byAt) {
    emit('onPick', { itemType: 'project', data: item })
  }
  if (composer.currentSelectedList.find(item => item.itemType === 'project')) return
  composer.currentSelectedList.push({
    label: '当前工程',
    name: '当前工程',
    enName: "codebase",
    itemType: 'project',
    icon: 'codebase-icon'
  })
}


function onClickWorkItem(workItem, byAt = false) {
  composer.currentSelectedList = composer.currentSelectedList.filter(item => {
    if (item.itemType === 'workItem') {
      if (item.id === workItem.id) return false
    }
    return true
  })
  composer.currentSelectedList.push({
    ...workItem,
    id: workItem.id,
    workItemKey: workItem.workItemKey,
    name: workItem.workItemKey,
    itemType: 'workItem', // 目录
    icon: 'icon-workitem'
  })
  if (byAt) {
    emit('onPick', { itemType: 'workItem', data: { ...workItem, name: workItem.workItemKey } })
  }
  dropdownRef.value.handlePopupVisibleChange(false)
}



function onClickDirItem(dirItem, byAt = false) {
  composer.currentSelectedList = composer.currentSelectedList.filter(item => {
    if(item.itemType === 'dir') {
      if(item.path === dirItem.path) return false
    }
    return true
  })

  composer.currentSelectedList.push({
    name: dirItem.name,
    path: dirItem.path,
    itemType: 'dir', // 目录
    icon: 'icon-folder-new'

  })
  if (byAt) {
    emit('onPick', { itemType: 'dir', data: dirItem })
  }
  dropdownRef.value.handlePopupVisibleChange(false)
}


function onClickFileItem(lastChangedFile, byAt = false) {
  const allCodeSize = (() => {
    let s = 0
    composer.currentSelectedList.forEach(f => {
      if (f.itemType === 'file') {
        s += f.size
      }
    })
    return s
  })()

  if (lastChangedFile) {
    if (composer.currentSelectedList.find(f => f.itemType === 'file' && f.path === lastChangedFile.path && f.startLine === lastChangedFile.startLine && f.endLine === lastChangedFile.endLine)) {
      // 文件已存在
      if (byAt) {
        emit('onPick', { itemType: 'file', data: lastChangedFile })
      }
    } else {
      if (allCodeSize + lastChangedFile.size < chatStore.codeSizeLimit) {
        composer.currentSelectedList.push({ ...lastChangedFile, itemType: 'file' })
        if (byAt) {
          emit('onPick', { itemType: 'file', data: lastChangedFile })
        }
      } else {
        Message.info('超出长度限制，请精简内容')
      }
    }
  }
  dropdownRef.value.handlePopupVisibleChange(false)

}
function onClickBack() {
  listType.value = 'main'
  searchKeyword.value = ''
}

let show = false
function onPopupVisibleChange(v) {
  show = v
  emit('onRefVisibleChange', v)
  if (v) {
    loadWorkSpaceData()
    loadWorkItems()
  } else {
    setTimeout(()=>{
      listType.value = 'main'
      searchKeyword.value = ''
      $EventBus.emit('clearMessageInputAt')
      isShowByAt.value = false
      document.removeEventListener('keydown', keydownEvent);
    }, 100)
  }
}

async function loadWorkItems() {
  const { data } = await sender.postRequest({
    command: 'workitem-request',
    _command: 'workitem-response',
    data: {
      reqType: 'searchworkitems'
    }
  })
  console.log('loadWorkItems', data)
  chatStore.workItems = data.workItemList || []
  chatStore.workItemError = data.error || ''
}


async function loadWorkSpaceData() {
  console.log('loadWorkSpaceData-start',)

  const { data } = await sender.postRequest({
    command: 'get-ide-utils-request',
    _command: 'get-ide-utils-response',
    data: {
      reqType: 'getchatcontexts'
    }
  })

  console.log('loadWorkSpaceData', data)
  composer.recentlyUsedFiles = (data.recentlyUsedFiles || []).map((item, index) => {
    return {
      ...item,
      isCurrentFile: (data.currentIndex || 0) === index
    }
  });
  composer.fileTree = data.fileTree ? [data.fileTree] : []
  composer.folderList = data.folderList || [];
}


function showRefList() {
  if (show) return
  if (addButtonRef.value) {
    addButtonRef.value.click()
    isShowByAt.value = true
    showByAtIndex.value = 0
    document.addEventListener('keydown', keydownEvent);
  }
}

function keydownEvent(event) {
  if (event.key === 'ArrowUp') {
    if (showByAtIndex.value > 0) showByAtIndex.value--
    event.preventDefault();
  } else if (event.key === 'ArrowDown') {
    const len = (() => {
      if (props.quickKeyword) return showByAtAllList.value.length
      else {
        if (listType.value === 'main') {
          return mainList.value.length
        } else if (listType.value === 'file') {
          return fileList.value.length
        } else if (listType.value === 'dir') {
          return dirList.value.length
        } else if(listType.value === 'workItem') {
          return workItemList.value.length
        }
      }
      return showByAtAllList.value.length
    })()
    if (showByAtIndex.value < len - 1) showByAtIndex.value++
    event.preventDefault();
  } else if (event.keyCode == 13 || event.key === '13') {
    onEnterPick()
    event.preventDefault();
  }
  const mf = document.querySelector('.composer-actived-mf')

  const container = document.querySelector('.composer-input-box-root .arco-scrollbar-container.arco-dropdown-list-wrapper')
  if (!mf || !container) return

  if (!isElementVisible(mf, container)) {
    const targetScrollTop = calculateScrollPosition(mf, container);
    console.log('targetScrollTop', targetScrollTop)
    container.scrollTo({ top: targetScrollTop, behavior: 'smooth' });
  }
}

function onEnterPick() {
  let item = null
  if (props.quickKeyword) {
    item = showByAtAllList.value[showByAtIndex.value]
  } else {
    if (listType.value === 'main') {
      item = mainList.value[showByAtIndex.value]
    } else if (listType.value === 'file') {
      item = fileList.value[showByAtIndex.value]
    } else if (listType.value === 'dir') {
      item = dirList.value[showByAtIndex.value]
    } else if (listType.value === 'workItem') {
      item = workItemList.value[showByAtIndex.value]
    }
  }
  if (!item) return
  onClickItem(item)

}

function hideRefList() {
  if (show) {
    if (addButtonRef.value) {
      addButtonRef.value.click()
    }
  }
}
function onClickLink(item) {
  console.log('打开工作项link', item)
  sender.postMessage({
    command: WebViewReqCommand.OPEN_EXTERNAL,
    data: {
      path: item.url,
      pureUrl: sender.isJet
    }
  })
}

defineExpose({
  showRefList,
  hideRefList
});



</script>
<style lang="scss">
.lib-select {

  // .arco-dropdown-option-active, .arco-dropdown-option:not(.arco-dropdown-option-disabled):hover {
  //   background-color: var(--vscode-textCodeBlock-background);
  // }
  .add-button {
    display: flex;
    align-items: center;
    cursor: pointer;
    flex-wrap: wrap;
    row-gap: 5px;
    gap: 5px;

    button.add-button-item {
      &:hover {
        // color: rgba(48, 124, 251, 1);
        // background-color: rgba(230, 239, 254, 1);

        // filter: contrast(150%);
      }
    }
  }

  .add-icon {
    width: 24px;
    height: 24px;
    align-items: center;
    border-radius: 3px;
    display: inline-flex;
    justify-content: center;
    border: 1px solid var(--cf-input-border);
  }

  .button-text {
    color: var(--vscode-input-foreground);
  }

  .button-icon {
    color: var(--vscode-input-foreground);
  }

  .lib-org {
    width: 100px;
    text-overflow: ellipsis;
    overflow: hidden;
    text-wrap: nowrap;
    margin-left: 20px;
    text-align: right;
    opacity: 0.7;
    font-size: 12px;
  }

}

.dropdown-footer-search-bar {
  box-sizing: border-box;
  padding: 12px;
}

.sub-back-text {
  display: none;
  font-size: 12px;
  color: var(--vscode-textLink-activeForeground);
}

.sub-back-bar {

  &:hover {
    background-color: var(--vscode-textCodeBlock-background) !important;

    .sub-back-text {
      display: block;
    }
  }

  // background-color: var(--vscode-textCodeBlock-background);
}

.lib-name-icon {
  color: var(--vscode-textLink-foreground) !important;
}

.main-list-bar {
  .main-list-item-tips {
    display: none;
    font-size: 12px;
    color: var(--vscode-textLink-activeForeground);
  }

  .main-list-item-tips-show {
    font-size: 12px;
    color: var(--vscode-textLink-activeForeground);
  }

  &.composer-actived-mf {
    background-color: var(--vscode-textCodeBlock-background) !important;

    .main-list-item-icon {
      display: none;
    }

    .main-list-item-tips {
      display: inline-block;
    }
  }

  &:hover {
    background-color: var(--vscode-textCodeBlock-background) !important;

    .main-list-item-icon {
      display: none;
    }

    .main-list-item-tips {
      display: inline-block;
    }
  }

}

.lib-item-bar {
  .lib-item-sub-text {
    display: block;
  }

  .lib-item-sub-tips {
    display: none;
    font-size: 12px;
    color: var(--vscode-textLink-activeForeground);
  }

  .list-item-sub-current {
    font-size: 12px;
    color: var(--vscode-textLink-activeForeground);
  }

  &.composer-actived-mf {
    background-color: var(--vscode-textCodeBlock-background) !important;

    .lib-item-sub-text {
      display: none;
    }

    .list-item-sub-current {
      display: none;
    }

    .lib-item-sub-tips {
      display: block;
    }
  }

  &:hover {
    background-color: var(--vscode-textCodeBlock-background) !important;

    .lib-item-sub-text {
      display: none;
    }

    .list-item-sub-current {
      display: none;
    }

    .lib-item-sub-tips {
      display: block;
    }
  }
}
</style>