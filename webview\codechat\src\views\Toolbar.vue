<template>
  <div class="toolbar-icon-box" v-if="chatStore.codeFreeLoginStatus">

    <a-tooltip content="新建会话" position="tr" mini :arrow-style="{ display: 'none' }">
      <span class="menu-icon" @click="createConversation">
        <svg-icon name="create-conv"></svg-icon></span>
    </a-tooltip>

    <template v-if="chatStore.tab === 'chat'">
      <a-tooltip content="指令模板" position="tr" mini :arrow-style="{ display: 'none' }" popup-container=".app-box">
        <span class="menu-icon"
          :class="{ 'active-btn': directiveTemplateOpened, 'hightlight-text': directiveTemplateOpened }"
          @click="chatStore.toggleDirectiveTemplate">
          <svg-icon name="prompt-template"></svg-icon></span>
      </a-tooltip>
      <a-tooltip content="历史对话" position="br" mini :arrow-style="{ display: 'none' }" popup-container=".app-box">
        <span class="menu-icon" @click="chatStore.toggleConversation"
          :class="{ 'active-btn': conversationOpened, 'hightlight-text': conversationOpened }">
          <svg-icon name="worktimerecord" size="14px" />
        </span>
      </a-tooltip>
      <a-tooltip content="模板中心" position="tr" mini :arrow-style="{ display: 'none' }" style="margin-right: 5px;">
        <span class="menu-icon" @click="openTemplateCenter">
          <svg-icon name="template-center"></svg-icon></span>
      </a-tooltip>
    </template>
  </div>

</template>
<script setup lang="js">
import { useChatStore } from '@/store/chat';
import { useComposer } from '@/store/composer'
import * as sender from '@/MessageSender'
import { computed, getCurrentInstance } from 'vue';
import { WebViewReqCommand } from '@/constants/common';

const app = getCurrentInstance();
const $EventBus = app?.appContext.config.globalProperties.$EventBus;

const emit = defineEmits(['createNewConv']);

const chatStore = useChatStore();
const composer = useComposer()

const directiveTemplateOpened = computed(() => chatStore.promptView === 'directiveTemplate')
const conversationOpened = computed(() => chatStore.promptView === 'conversation')

// 打开模板中心
function openTemplateCenter() {
  sender.postMessage({
    command: WebViewReqCommand.OPEN_EXTERNAL,
    data: {
      path: '/smartassist/codefree?templateCenterOpen=1'
    }
  })

}
// 创建新会话
function createConversation() {
  if(chatStore.tab === 'chat') {
    emit('createNewConv');
  } else {
    composer.createChat()
    chatStore.workItems = []
    $EventBus.emit('clearInputBox');
    console.log('composer.createChat()')
  }
}


</script>
<style lang="scss">
.toolbar-icon-box {
  display: flex;
  justify-content: flex-end;
  text-align: right;
  align-items: center;

  .menu-icon {
    box-sizing: border-box;
    width: 24px;
    margin: 0 1px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--vscode-inputOption-activeForeground);

    &.active-btn {
      background-color: var(--vscode-list-inactiveSelectionBackground);
      border-radius: 3px;
    }

    &:hover {
      background-color: var(--vscode-toolbar-hoverBackground);
      // color: var(--vscode-textLink-foreground);
      border-radius: 3px;
    }
  }
}
</style>