import { QuestionType } from '../common/constants';
import { AskQuestionParams } from '../service/types/questionTask';
import { Logger } from '../utils/logger';
import { AutocompleteParams, HistoryAnswer, AutocompleteResult } from './types';
import { IStatusBarHandler } from '../statusbar/types';
import { getEndOfLine } from '../utils/common';
import { END_OF_LINE } from '../common/config';
import { CodeCompletionStrategy } from './completionStrategy/codeCompletionStrategy';
import { DefaultCodeCompletionStrategy } from './completionStrategy/defaultCodeCompletionStrategy';
import { AgentCodeCompletionStrategy } from './completionStrategy/agentCompletionStrategy';

export class CodeCompleteEngin {
  private strategies: Map<string, CodeCompletionStrategy>;

  private statusHandler: IStatusBarHandler | null = null;

  // 上次位置标识
  private lastReqParamKey = '';

  // 当前位置标识
  private curReqParamKey = '';

  // 位置标识与回答列表映射
  private reqParamKeyAnswerMap: Map<string, HistoryAnswer[]> = new Map<string, HistoryAnswer[]>();

  constructor() {
    this.strategies = new Map<string, CodeCompletionStrategy>();
    this.strategies.set('default', new DefaultCodeCompletionStrategy());
    this.strategies.set('agent', new AgentCodeCompletionStrategy());
  }

  /**
   * 设置enginHandler，目前只通知事件到statusbar
   * @param handler
   */
  public setStatusBarHandler(handler: IStatusBarHandler | null) {
    this.statusHandler = handler;
  }

  /**
   * 发起代码补全请求
   * @param request
   * @returns
   */
  public async askCompletion(request: AutocompleteParams) {
    this.curReqParamKey = this.getReqParamKey(request);

    var result;
    // if (this.strategies.get('agent')?.isEnabled()) {
    //   Logger.info("[CodeCompleteEngin]: ask from agent")
      result = await this.strategies.get('agent')?.getCodeCompletion(this, request)
    // } else {
      // Logger.info("[CodeCompleteEngin]: ask from default")
    //   result = await this.strategies.get('default')?.getCodeCompletion(this, request)
    // }

    return result;
  }

  /**
   * 清除同一光标位置历史回答
   * @param reqParamKey
   */
  public clearReqParamKeyAnswerCache(reqParamKey?: string) {
    if (reqParamKey) {
      this.reqParamKeyAnswerMap.delete(reqParamKey);
    } else {
      this.reqParamKeyAnswerMap.clear();
    }
  }

  /**
   * 根据不同场景获取不同的stopWords
   * @param questionType 请求类型
   * @param request 请求参数
   */
  public getStopWords(
    questionType: QuestionType,
    request: AutocompleteParams
  ): string[] | undefined {
    let stopWords, words;
    const { isMidline, endOfLine } = this.checkIsMidlinePosition(request.before, request.after);

    switch (questionType) {
      // 自动补全：当前行的中间触发，取当前位置后面实质字符; 其它情况取'\n'
      case QuestionType.CODE_GEN:
        words = isMidline ? request.after.split(endOfLine)[0] : END_OF_LINE;
        break;
      // 手动补全：
      // 1) 当前行的中间触发，取当前位置后面实质字符;
      // 2) 行尾触发寻找当前位置所在行以下的第一个非空行;如果都是空行，取'\n'
      case QuestionType.CODE_GEN_MANUAL:
        if (isMidline) {
          words = request.after.split(endOfLine)[0];
        } else {
          const lineArr = request.after.split(endOfLine);
          const idx = lineArr.findIndex(line => !!line);

          if (idx > -1 && endOfLine) {
            words = lineArr.slice(0, idx + 1).join(endOfLine);
          } else {
            words = END_OF_LINE;
          }
        }
        break;
      default:
        break;
    }

    if (words) {
      if (Array.isArray(words)) {
        stopWords = words;
      } else { 
        stopWords = [words];
      }
    }

    return stopWords;
  }

  /**
   * 构建代码补全返回数据结构
   * @param answer
   * @returns
   */
  public buildCodeCompleteResult(
    answer = '',
    isCurReqId?: boolean,
    curReqParams?: AskQuestionParams
  ): AutocompleteResult | undefined {
    let newAnswers: string[] = [],
      historyAnswers: HistoryAnswer[] = [];

    const { isSamePosition, curIsAuto, curKey, lastKey } = this.checkIsSamePosition();

    // 光标位置不变，先获取历史回答
    if (isSamePosition && lastKey) {
      historyAnswers = this.reqParamKeyAnswerMap.get(lastKey) || [];

      // 当前是自动触发，需要去除手动补全的回答
      if (curIsAuto) {
        historyAnswers = historyAnswers.filter(ans => !!ans.isAuto);
      }
    } else {
      // 光标位置改变，可以清除光标位置历史回答
      this.clearReqParamKeyAnswerCache();
    }

    // 当前回答有效，分情况返回答案
    if (answer) {
      // 自动补全时，只保留一行回答
      const newAnswer = curIsAuto ? this.filterAutoAnswer(answer) : answer;
      const newHistoryAnswer = { answer: newAnswer, isAuto: curIsAuto };
      const answerHasExisted = !!historyAnswers.find(ans => ans.answer === answer);

      // 1) 光标位置不变且是当前问题，当前回答和历史一起返回
      if (isSamePosition && isCurReqId) {
        newAnswers = historyAnswers.map(ans => ans.answer);

        if (!answerHasExisted) {
          newAnswers = [newAnswer].concat(newAnswers);
          historyAnswers.unshift(newHistoryAnswer);
          this.reqParamKeyAnswerMap.set(curKey, historyAnswers);
        }
      } else if (isSamePosition && !isCurReqId && !answerHasExisted) {
        // 2) 光标位置不变但不是当前问题，记录到历史回答，不返回
        historyAnswers.unshift(newHistoryAnswer);
        this.reqParamKeyAnswerMap.set(curKey, historyAnswers);
      } else if (!isSamePosition && isCurReqId) {
        // 3) 光标位置变化且是当前问题，记录到新位置历史回答，当前回答返回
        newAnswers.unshift(newAnswer);
        this.reqParamKeyAnswerMap.set(curKey, [newHistoryAnswer]);
      } else {
        // 4) 其它，忽略不处理
      }
    }

    if (isCurReqId) {
      this.lastReqParamKey = this.curReqParamKey;
    }

    if (newAnswers.length > 0) {
      const oldPrefix = isCurReqId && curReqParams ? curReqParams.prefix || '' : '';

      return {
        old_prefix: oldPrefix || '',
        results: newAnswers.map(ans => ({
          new_prefix: oldPrefix + ans,
          old_suffix: '',
          new_suffix: '',
          answer: ans,
        })),
      };
    }

    return undefined;
  }

  /**
   * 获取当前问题光标位置标识
   * @param request
   * @returns
   */
  private getReqParamKey(request: AutocompleteParams) {
    return `${request.filename}__${request.line}__${request.character}??${request.isAuto}`;
  }

  /**
   * 分隔光标位置标识，返回位置信息和是否自动补全
   * @param keyStr
   * @returns
   */
  private splitReqParamKey(keyStr: string) {
    if (!keyStr) {
      return {};
    }

    const splitArr = keyStr.split('??');

    return {
      key: splitArr[0],
      isAuto: splitArr[1] === 'true',
    };
  }

  /**
   * 检查光标位置是否没变
   * @returns
   */
  private checkIsSamePosition() {
    const { key: curKey, isAuto: curIsAuto } = this.splitReqParamKey(this.curReqParamKey);
    const { key: lastKey, isAuto: lastIsAuto } = this.splitReqParamKey(this.lastReqParamKey);

    return {
      isSamePosition: lastKey && curKey === lastKey,
      curKey: curKey as string,
      curIsAuto: !!curIsAuto,
      lastKey,
      lastIsAuto,
    };
  }

  /**
   * 自动补全应去掉\n或\r\n后面的值，且不包含\n以及\r\n
   * @param answer
   */
  private filterAutoAnswer(answer: string): string {
    const endOfLine = getEndOfLine(answer);

    if (endOfLine) {
      return answer.split(endOfLine)?.[0] || '';
    }

    return answer;
  }

  /**
   * 发起补全的位置是否在行中以及文本的换行符
   * @param prefix
   * @param suffix
   * @returns
   */
  private checkIsMidlinePosition(
    prefix: string,
    suffix: string
  ): { isMidline: boolean; endOfLine: string } {
    const endOfLine = getEndOfLine(prefix + suffix);

    return {
      isMidline: suffix.indexOf(endOfLine) > 0,
      endOfLine,
    };
  }

  /**
   * 正则表达式匹配整个字符串，确保它只包含换行符(\n)和空格
   * @param answer
   * @returns
   */
  private isOnlyNewlinesOrSpaces(answer: string): boolean {
    const regex = /^[\n ]*$/;
    return regex.test(answer);
  }

  /**
   * 定制化处理代码补全结果
   * @param answer
   * @returns
   */
  public handleCompleteAnswer(answer: string) {
    Logger.debug(
      `[CodeCompleteEngin] handleCompleteAnswer, answer:${answer}, length:${answer.length}`
    );
    if (this.isOnlyNewlinesOrSpaces(answer)) {
      Logger.debug(
        `[CodeCompleteEngin] handleCompleteAnswer, isOnlyNewlinesOrSpaces:${this.isOnlyNewlinesOrSpaces(
          answer
        )}`
      );
      return '';
    }
    return answer;
  }

  public getStatusBarHandler(): IStatusBarHandler | null {
    return this.statusHandler;
  }
}

export const CodeCompleteEnginInst = new CodeCompleteEngin();
