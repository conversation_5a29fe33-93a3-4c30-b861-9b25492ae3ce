import * as vscode from 'vscode';
import { getFullFunctionRange, languageRegexes } from './functionParser';
import { SrdCommand } from '../common/constants';



export class FunctionCodeLensProvider implements vscode.CodeLensProvider {

  private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();

  public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;

  public provideCodeLenses(document: vscode.TextDocument, token: vscode.CancellationToken): vscode.CodeLens[] {
    const codeLenses: vscode.CodeLens[] = [];
    // Regular expression to match files like app.xxx.xxx.js or chunk-xxxx.xxxx.js
    const staticFilePattern = /(app\.\w+\.\w+\.js|chunk-\w+\.\w+\.js)$/;

    // Skip static files if the filename matches the pattern
    if (staticFilePattern.test(document.fileName)) {
      // console.log(`[secidea] Skipping static JS file: ${document.fileName}`);
      // return null;
      return codeLenses;
    }

    // console.log('[secidea] provideCodeLenses called');
    
    const config = vscode.workspace.getConfiguration('代码助手');
    const isCodeLensEnabled = config.get('是否展示函数级别快捷键', true);
    console.log('[secidea] CodeLens enabled:', isCodeLensEnabled);

    if (!isCodeLensEnabled) {
      console.log('[secidea] CodeLens is disabled, returning empty array');
      return codeLenses;
    }

    const text = document.getText();
    const languageId = document.languageId;
    console.log('[secidea] Language ID:', languageId);

    const functionRegex = languageRegexes[languageId];
    if (!functionRegex) {
      console.log('[secidea] Unsupported language:', languageId);
      return codeLenses;
    }

    let match;
    let lastIndex = -1;
    while ((match = functionRegex.exec(text))) {
      if (match.index === lastIndex) {
        functionRegex.lastIndex++;
        continue;
      }
      lastIndex = match.index;

      const startPos = document.positionAt(match.index);
      const fullFunctionRange = getFullFunctionRange(document, startPos);

      if (fullFunctionRange) {
        const functionName = match.slice(1).find(m => m !== undefined) || 'Anonymous Function';
        console.log(`[secidea] Found function: ${functionName} at line ${startPos.line + 1}`);

        const functionCode = this.standardizingCode(document.getText(fullFunctionRange), languageId);
        
        // Create CodeLens for each action
        const actions = ['解释代码', '生成单元测试', '生成代码注释', '生成代码优化建议'];
        
        actions.forEach(action => {
          codeLenses.push(
            new vscode.CodeLens(fullFunctionRange, {
              title: action,
              command: SrdCommand.CODELENS_ACTION,
              arguments: [action, fullFunctionRange, functionCode],
            })
          );
        });
      }
    }

    console.log(`[secidea] Returning ${codeLenses.length} CodeLenses`);
    return codeLenses;
  }

  public refresh(): void {
    this._onDidChangeCodeLenses.fire();
  }

  public standardizingCode(code: string, languageId: string) { 

    if (!/\n$/.test(code)) {
      code = `${code}\n`;
    }

    return `\`\`\`${languageId}\n${code}\`\`\``;
    
  }
}


