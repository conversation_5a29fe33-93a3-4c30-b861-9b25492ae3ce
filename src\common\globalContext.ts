import { ExtensionContext, extensions } from 'vscode';
import { GlobalStateKey, SecretStorageKey } from './constants';
import { str2obj } from '../utils/common';
import { UInfo } from '../service/types/login';
import { CodeAIResponseConfig } from '../service/types/codeAI';
import { isRunInCloudIDE } from '../adapter/common';
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '../utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import { agentConfig } from '../service/types/codeAI';

let globalContext: ExtensionContext;

export function setGlobalContext(context: ExtensionContext) {
  globalContext = context;
}

export function getGlobalContext() {
  return globalContext;
}

/**
 * 获取插件版本
 * @returns
 */
export function getExtensionVersion() {
  const extension = process.env.ISSEC !== 'false' ? extensions.getExtension('Secidea.Secidea') : extensions.getExtension('srdcloud.srd-copilot-vscode');

  if (extension) {
    return extension.packageJSON.version;
  }

  return '';
}

/**
 * 获取git仓库配置
 */

export function getGitRepoUrl() {
  try {
    // 获取工作区根目录
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0].uri.fsPath;
    /*********测试代码 */
    Logger.debug(
      `vscode.workspace.workspaceFolders: ${JSON.stringify(vscode.workspace.workspaceFolders)}`
    );
    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      const filePath = activeEditor.document.uri.fsPath;
      Logger.debug(filePath);
    }

    /*****测试代码*****/

    // 构建.git/config文件的路径
    const gitConfigPath = path.join(workspaceFolder || '', '.git', 'config');

    // 读取配置文件内容
    const configContent = fs.readFileSync(gitConfigPath, 'utf8');

    // 正则表达式匹配远程URL
    const regex = /url = (.*)/;
    const match = regex.exec(configContent);

    if (match && match.length > 1) {
      return match[1];
    } else {
      return '';
    }
  } catch (error) {
    return '';
  }
}

// 根据git Remote URL获取git仓库签名, 用于生成AI Commit Message
export function getGitRepoSignature() { 
  const workspaceFolders = vscode.workspace.workspaceFolders;
  if (!workspaceFolders) {
    Logger.error(`[getGitRepoSignature] workspaceFolder is empty!`);
    return null;
  }
  const getGitRemoteUrl = getGitRepoUrl();
  const projectSignature = extractProjectCode(getGitRemoteUrl);
  return projectSignature;
}

function extractProjectCode(gitUrl: string | null): string | null {
  if (!gitUrl || !gitUrl.includes("srdcloud.cn")) {
    return null;
  }

  // 截取 srdcloud.cn 之后的内容
  const startIndex = gitUrl.indexOf("srdcloud.cn") + "srdcloud.cn".length;
  let pathPart = gitUrl.substring(startIndex);

  // 去除开头的 / 或 :
  if (pathPart.startsWith("/") || pathPart.startsWith(":")) {
    pathPart = pathPart.substring(1);
  }

  // 去除最后一个 / 及之后的内容
  const lastIndex = pathPart.lastIndexOf("/");
  if (lastIndex === -1) {
    return null; // 没有足够层级
  }

  const projectPath = pathPart.substring(0, lastIndex);

  // 按 / 分割路径，过滤掉无效前缀部分（如纯数字、"a"、"subB" 等）
  const parts = projectPath.split("/");
  const resultParts: string[] = [];

  let validStart = false;
  for (const part of parts) {
    if (!/^\d+$/.test(part) && part !== "a" && part !== "subB") {
      validStart = true;
      resultParts.push(part);
    } else if (!validStart) {
      continue; // 跳过无效前缀
    }
  }

  return resultParts.length > 0 ? resultParts.join("/") : null;
}

// 获取远程仓库列表1
const execAsync = promisify(exec) as (command: string, options?: { cwd?: string }) => Promise<{ stdout: string; stderr: string }>;

export async function getGitRemoteUrls(): Promise<string[]> {
  try {
    // 获取当前工作区根目录
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
      Logger.error(`[getGitRemoteUrls] workspaceFolder is empty!`);
      return [];
    }
    const workspaceRoot = workspaceFolders[0].uri.fsPath;
    const { stdout } = await execAsync('git remote -v', {
      cwd: workspaceRoot
    });

    const remotes = stdout
      .split('\n')
      .filter(line => line.includes('(fetch)'))
      .map(line => {
        const parts = line.split(/\s+/);
        return parts[1];
      });
    return [...new Set(remotes)];
  } catch (error) {
    Logger.error(`[getGitRemoteUrls] Failed to get git remotes:${error}`);
    return [];
  }
}

export function getProjectName() {
  const { workspaceFolders } = vscode.workspace;
  if (workspaceFolders && workspaceFolders.length > 0) {
    const firstFolder = workspaceFolders[0];
    const projectName = path.basename(firstFolder.uri.fsPath);
    return projectName;
  }
  return '';
}

export async function getMacAddress() {
  const macAddr = require('macaddress');
  return await macAddr.one().then((mac: string) => {
    return mac;
  });
}

/**
 * 获取本地用户信息
 * @returns
 */
export async function getCurrentUser(): Promise<UInfo> {
  let storeVal;

  if (isRunInCloudIDE()) {
    storeVal = getGlobalContext().globalState.get(GlobalStateKey.CURRENT_USER) as string;
  } else {
    storeVal = await getGlobalContext().secrets.get(SecretStorageKey.CURRENT_USER);
  }

  return str2obj(storeVal);
}

/**
 * 设置本地用户信息
 */
export async function setCurrentUser(info: UInfo) {
  if (isRunInCloudIDE()) {
    await getGlobalContext().globalState.update(GlobalStateKey.CURRENT_USER, JSON.stringify(info));
  } else {
    await getGlobalContext().secrets.store(SecretStorageKey.CURRENT_USER, JSON.stringify(info));
  }
}

/**
 * 清空本地用户信息
 */
export async function clearCurrentUser() {
  if (isRunInCloudIDE()) {
    await getGlobalContext().globalState.update(GlobalStateKey.CURRENT_USER, undefined);
  } else {
    await getGlobalContext().secrets.delete(SecretStorageKey.CURRENT_USER);
  }
}

/**
 * 设置Authentication认证token
 * @param token
 */
export async function setAuthToken(token: string) {
  if (isRunInCloudIDE()) {
    await getGlobalContext().globalState.update(GlobalStateKey.AUTH_TOKEN, token);
  } else {
    await getGlobalContext().secrets.store(SecretStorageKey.AUTH_TOKEN, token);
  }
}

/**
 * 获取Authentication认证token
 * @param token
 */
export async function getAuthToken(): Promise<string | undefined> {
  if (isRunInCloudIDE()) {
    return getGlobalContext().globalState.get(GlobalStateKey.AUTH_TOKEN);
  } else {
    return await getGlobalContext().secrets.get(SecretStorageKey.AUTH_TOKEN);
  }
}

/**
 * 清空Authentication认证token
 * @param token
 */
export async function clearAuthToken() {
  if (isRunInCloudIDE()) {
    return getGlobalContext().globalState.update(GlobalStateKey.AUTH_TOKEN, undefined);
  } else {
    return await getGlobalContext().secrets.delete(SecretStorageKey.AUTH_TOKEN);
  }
}

/**
 * 获取模型相关配置
 * @returns
 */
export function getCodeAIConfig(): CodeAIResponseConfig | undefined {
  const config = getGlobalContext().globalState.get(GlobalStateKey.CODEAI_CONFIG);

  return config ? (config as CodeAIResponseConfig) : undefined;
}

/**
 * 设置模型相关配置
 * @param config
 */
export async function setCodeAIConfig(config: CodeAIResponseConfig | undefined) {
  await getGlobalContext().globalState.update(GlobalStateKey.CODEAI_CONFIG, config);
}

/**
 * 设置agent版本
 * @param version
 */
export async function setAgentVersion(version: agentConfig[] | undefined) {
  await getGlobalContext().globalState.update(GlobalStateKey.AGENT_VERSION, version);
}

/**
 * 获取agent版本
 * @returns
 */
export function getAgentVersion(): agentConfig[] | undefined {
  const config = getGlobalContext().globalState.get(GlobalStateKey.AGENT_VERSION);
  return config ? (config as agentConfig[]) : undefined;
}



