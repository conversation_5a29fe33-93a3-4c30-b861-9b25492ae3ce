import * as vscode from 'vscode';
import * as path from 'path';
import SrdStatusBar from './srdStatusBar';
import { RtnCode, RtnMessage, SrdCommand } from '../common/constants';
import { LoginServiceInst } from '../service/loginService';
import { FORBIDDEN_PATH, NO_SUBSCRIBE_PATH, HELPDOC_PATH } from '../common/config';
import AutoCompleteStatusStore from './autoCompleteStatusStore';
import { getHttpServerHost } from '../utils/envUtil';

/**
 * 注册状态栏
 * @param context context
 */
export function registerSrdStatusBar(context: vscode.ExtensionContext) {
  // 初始自动补全状态
  AutoCompleteStatusStore.initEnabled();

  // 初始化状态栏
  const srdStatusBar = new SrdStatusBar(context);
  srdStatusBar.setObservable(LoginServiceInst);
  srdStatusBar.init();

  /**
   * 注册状态栏
   */
  context.subscriptions.push(srdStatusBar);

  /**
   * 注册服务异常展示
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.SHOW_SVR_ERROR, async (code: RtnCode) => {
      const selection = await vscode.window.showInformationMessage(
        RtnMessage[code],
        '详情',
        '取消'
      );

      if (selection === '详情') {
        const path = code === RtnCode.INVALID_USER ? NO_SUBSCRIBE_PATH : FORBIDDEN_PATH;
        const uri = `${getHttpServerHost()}${path}`;
        vscode.env.openExternal(vscode.Uri.parse(uri));
      }
    })
  );

  /**
   * 注册启用补全命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.TOGGLE_CODECOMPLETE, async () => {
      const enabled = await AutoCompleteStatusStore.checkIfEnabled();

      if (enabled) {
        await AutoCompleteStatusStore.setEnabled(false);
        srdStatusBar.setIfEnabled(false);
      } else {
        await AutoCompleteStatusStore.setEnabled(true);
        srdStatusBar.setIfEnabled(true);
      }
    })
  );

  /**
   * 注册打开帮助文档命令
   */
  console.log('[DEBUG] Registering OPEN_HELPDOC command:', SrdCommand.OPEN_HELPDOC);
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.OPEN_HELPDOC, () => {
      console.log('[DEBUG] OPEN_HELPDOC command triggered');
      console.log('[DEBUG] process.env.ISSEC:', process.env.ISSEC);

      if (process.env.ISSEC !== 'false') {
        // 如果是 Secidea 品牌，打开本地 HTML 文件
        const localHtmlPath = path.join(__dirname, '..', '..', 'page', 'index.html');
        console.log('[DEBUG] Local HTML path:', localHtmlPath);

        try {
          const uri = vscode.Uri.file(localHtmlPath);
          console.log('[DEBUG] URI:', uri.toString());
          vscode.env.openExternal(uri);
          console.log('[DEBUG] openExternal called successfully');
        } catch (error) {
          console.error('[DEBUG] Error opening local file:', error);
          vscode.window.showErrorMessage(`无法打开帮助文档: ${error}`);
        }
      } else {
        // 否则打开外部链接
        const uri = `${getHttpServerHost()}${HELPDOC_PATH}`;
        console.log('[DEBUG] External URI:', uri);
        vscode.env.openExternal(vscode.Uri.parse(uri));
      }
    })
  );

  /**
   * 注册状态栏菜单命令
   */
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.SHOW_STATUS_MENU, async (state: any, code?: number) => {
      const items: vscode.QuickPickItem[] = [];

      // 根据状态添加相应的菜单项
      if (state === 'CodeCompleteEnabled') {
        items.push({
          label: '$(circle-slash) 禁用自动补全',
          description: '点击禁用代码自动补全功能',
          detail: 'toggle'
        });
      } else if (state === 'CodeCompleteDisabled') {
        items.push({
          label: '$(check) 启用自动补全',
          description: '点击启用代码自动补全功能',
          detail: 'toggle'
        });
      } else if (state === 'NotLogin') {
        items.push({
          label: '$(sign-in) 登录',
          description: '点击进行登录',
          detail: 'login'
        });
      }

      // 始终显示设置选项
      items.push({
        label: '$(gear) 设置',
        description: '打开海云安代码助手设置',
        detail: 'settings'
      });

      // 添加帮助文档选项
      items.push({
        label: '$(question) 帮助文档',
        description: '查看帮助文档',
        detail: 'help'
      });

      const selected = await vscode.window.showQuickPick(items, {
        placeHolder: '选择操作',
        matchOnDescription: true
      });

      if (selected) {
        switch (selected.detail) {
          case 'toggle':
            vscode.commands.executeCommand(SrdCommand.TOGGLE_CODECOMPLETE);
            break;
          case 'login':
            vscode.commands.executeCommand(SrdCommand.LOGIN);
            break;
          case 'settings':
            vscode.commands.executeCommand(SrdCommand.SETTINGS);
            break;
          case 'help':
            vscode.commands.executeCommand(SrdCommand.OPEN_HELPDOC);
            break;
        }
      }
    })
  );

  return srdStatusBar;
}
