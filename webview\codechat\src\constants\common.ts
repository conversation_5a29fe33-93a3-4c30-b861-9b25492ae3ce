import { DirectiveTemplate, DirectiveType } from '@/types';
import brandConfig from '../../brand.json';

// 根据品牌配置确定显示名称
const brandName = brandConfig.isSec ? '海云安代码助手' : '研发云CodeFree';

export const FIRST_PROMPT =
  `我的名字是${brandName}，我使用中文进行交流，作为一个高度智能化的自然语言编程助手,我是由研发云团队使用最先进的技术和大量数据训练而成。\n` +
  '我的核心目标是以友好、简单、清晰的方式帮助用户解决编程问题。我拥有深厚的编程知识,涵盖各种流行的编程语言和框架,如Python、Java、JavaScript、C++等。我也掌握广泛的计算机科学知识,如数据结构、算法、操作系统、网络等。\n' +
  '对于用户提出的任何编程相关的问题,我都能给出最佳的解决方案。我会解析问题的本质,运用丰富的知识库推导出正确的代码实现。如果需要,我还会给出多种可选方案的对比分析。\n' +
  '最后,我会恪守对用户隐私的尊重,所有对话内容仅用于提升我自身的能力,不会泄露或记录任何用户个人信息。请尽管提出你的编程问题,我会提供最专业和有价值的帮助。\n' +
  '我会用中文来回答你的问题。';
export enum ChatTips {
  FIRST_PROMPT = `<div class="intro-note">我是${brandName}，您的智能开发助手。我可以在您编程时进行代码补全和编程问答，您也可以选中代码后右键或使用/触发快捷指令:</div>\n\n` +
  '<div class="button-group"><button class="custom_button" id="codeExplain">代码解释</button>\n<button class="custom_button" id="codeComment">生成代码注释</button>\n<button class="custom_button" id="codeTest">生成单元测试</button>\n<button class="custom_button" id="codeOptimazation">生成优化建议</button></div>\n\n' +
  '您可以尝试问我:\n\n' +
  `<a id="introHelpDoc" class="home-link">${brandName}可以做什么?</a> <a class="home-link" id="whatisnew">what's new?<span>new</span></a>`,
  FIRST_PROMPT_LOADED = `<div class="intro-note">我是${brandName}，您的智能开发助手。我可以在您编程时进行代码补全和编程问答，您也可以选中代码后右键或使用/触发快捷指令:</div>\n\n` +
  '<div class="button-group"><button class="custom_button" id="codeExplain">代码解释</button>\n<button class="custom_button" id="codeComment">生成代码注释</button>\n<button class="custom_button" id="codeTest">生成单元测试</button>\n<button class="custom_button" id="codeOptimazation">生成优化建议</button></div>\n\n' +
  '您可以尝试问我:\n\n' +
  `<a id="introHelpDoc" class="home-link">${brandName}可以做什么?</a> <a class="home-link" id="whatisnew">what's new?</a>`,
  // FIRST_PROMPT = '<div class="intro-note">我是研发云CodeFree，您的智能开发助手。我可以在您编程时进行代码补全和编程问答，您也可以选中代码后右键或使用/触发快捷指令:</div>\n\n' +
  // '<div class="button-group"><button class="custom_button" id="codeExplain">代码解释</button>\n<button class="custom_button" id="codeComment">生成代码注释</button>\n<button class="custom_button" id="codeTest">生成单元测试</button>\n<button class="custom_button" id="codeOptimazation">生成优化建议</button></div>\n\n' +
  // '您可以尝试问我:\n\n' +
  // '[CodeFree可以做什么?](#){#introHelpDoc}\n[Python如何遍历一个字典?](#){#introQuestion}',
  // FIRST_PROMPT_LOADED = '<div class="intro-note">我是研发云CodeFree，您的智能开发助手。我可以在您编程时进行代码补全和编程问答，您也可以选中代码后右键或使用/触发快捷指令:</div>\n\n' +
  // '<div class="button-group"><button class="custom_button" id="codeExplain">代码解释</button>\n<button class="custom_button" id="codeComment">生成代码注释</button>\n<button class="custom_button" id="codeTest">生成单元测试</button>\n<button class="custom_button" id="codeOptimazation">生成优化建议</button></div>\n\n' +
  // '您可以尝试问我:\n\n' +
  // '[CodeFree可以做什么?](#){#introHelpDoc}\n[Python如何遍历一个字典?](#){#introQuestion}',
  CONVERSATION_TITLE = '新的会话',
  DEFAULT_EMPTY_MSG = '请更换提问方式重新提问',
  LOGIN_MSG = `<span>我是${brandName}，您的智能开发助手。我可以在您编程时进行代码补全和编程问答，您也可以选中代码后右键或使用/触发快捷指令\n\n</span>` +
  '<button id="login">使用前请先登录</button>',
}
export const helpMsg =
  `您好，我是${brandName}，您的智能开发助手。让我和您一起更高效地完成编码工作。\n您可以：\n1. 在这里向我提问，建议精准详细表达问题，亦可以选中代码后输入问题\n2. 选择智能编程模式，提出您对当前工程的新需求或优化需求，我来为您完成代码文件的编写\n3. 在编辑器中获得代码补全建议\n4. 选中代码后<span class="yellow">右键</span>或在对话框中<span class="yellow">使用/</span>触发快捷指令\n5. 查看和使用指令模板\n\nwin快捷键：\n` +
  '1. Alt + Shift + K：打开/关闭问答\n2. Tab：采纳代码建议\n3. Alt + [：查看上一条补全建议\n4. Alt + ]：查看下一条补全建议\n5. Ctrl + Enter：手动发起补全请求' +
  '\n\nmac快捷键:\n1. ⌥⇧K: 打开/关闭问答框\n2. Tab: 采纳代码建议\n3. ⌥[: 查看上一条补全建议\n4. ⌥]: 查看下一条补全建议\n5. ⌘↩︎: 手动发起补全请求' +
  '\n\n快捷指令:\n1. /help: 了解CodeFree;\n2. /explain code: 解释选中的代码;\n3. /generate unit test: 为选中的代码生成单元测试;\n4. /generate comment: 为选中的代码生成方法注释或行间注释;\n5. /generate optimization: 为选中的代码生成代码优化建议及相关代码;' +
  '\n\n更多: \n[CodeFree帮助文档](#){#helpDoc}\n[问题反馈](#){#feedback}';

// export const helpMsg =
//   '您好，我是研发云CodeFree，您的智能开发助手。让我和您一起更高效地完成编码工作。\n您可以：\n1. 在这里向我提问，建议精准详细表达问题，亦可以选中代码后输入问题\n2. 在编辑器中获得代码补全建议\n3. 选中代码后<span class="yellow">右键</span>或在对话框中<span class="yellow">使用/</span>触发快捷指令\n4. 查看和使用指令模板\n\nwin快捷键：\n' +
//   '1. Alt + Shift + K：打开/关闭问答\n2. Tab：采纳代码建议\n3. Alt + [：查看上一条补全建议\n4. Alt + ]：查看下一条补全建议\n5. Ctrl + Enter：手动发起补全请求' +
//   '\n\nmac快捷键:\n1. ⌥⇧K: 打开/关闭问答框\n2. Tab: 采纳代码建议\n3. ⌥[: 查看上一条补全建议\n4. ⌥]: 查看下一条补全建议\n5. ⌘↩︎: 手动发起补全请求' +
//   '\n\n快捷指令:\n1. /help: 了解CodeFree;\n2. /explain code: 解释选中的代码;\n3. /generate unit test: 为选中的代码生成单元测试;\n4. /generate comment: 为选中的代码生成方法注释或行间注释;\n5. /generate optimization: 为选中的代码生成代码优化建议及相关代码;' +
//   '\n\n更多: \n[CodeFree帮助文档](#){#helpDoc}\n[问题反馈](#){#feedback}';

export const introQuestion = 'python如何遍历一个字典';
/** webview向vscode发起请求 */
export enum WebViewReqCommand {
  /** 开发问答-加载历史会话请求 */
  CONVERSATION_LOAD = 'conversation-load',
  /** 开发问答-新增会话请求 */
  CONVERSATION_ADD = 'conversation-add',
  /** 开发问答-切换回话请求 */
  CONVERSATION_SWITCH = 'conversation-switch',
  /** 开发问答-删除会话请求 */
  CONVERSATION_REMOVE = 'conversation-remove',
  /** 开发问答-编辑会话标题请求 */
  CONVERSATION_EDIT_TITLE = 'conversation-edit-title',
  /** 开发问答-发起提问请求 */
  CHAT_REQUEST = 'chat-request',
  /** 开发问答-登录请求 */
  LOGIN = 'login',
  /** 开发问答-请求插入代码到编辑器中 */
  INSERT_CODE = 'insert-code',
  /** 开发问答-请求插入单元测试代码到编辑器中 */
  INSERT_UNITTEST = 'insert-unittest',
  /** 开发问答-请求当前编辑器是否已选择代码，有，则返回 */
  RETRIVE_CODE_SELECTION = 'retrive-code-selection',
  /** 开发问答-停止回答 */
  CANCEL_CHAT_REQUEST = 'cancel-chat-request',

  /** 开发问答-上报统计数据 */
  DATA_REPORT = 'data-report',
  /** 判断是否已登录codefree */
  CHECK_IF_LOGIN = 'check-if-login',

  STOP_CHAT_REQUEST = 'stop-chat-request',
  CONVERSATION_FEEDBACK = 'conversation-feedback',
  CONVERSATION_REFRESH = 'conversation-refresh',
  WEBVIEW_LOADED = 'webview-loaded',
  KNOWLEDGE_BASE_REQUEST = 'knowledge-base-request',
  // 打开外部url
  OPEN_EXTERNAL = 'open-external',
  PROMPTS_REQUEST = 'prompts-request',
  CODE_SECURITY_SCAN_REQUEST = 'code-security-scan-request',
  QA_FOR_RELATED_FILES_REQUEST = 'qa-for-related-files-request',
  VIEW_DIFF = 'view-diff',
  OPEN_TEXT_DOCUMENT = 'open-text-document',
  INVOKE_TERMINAL_CAPABILITY = 'invoke-terminal-capability',
}

/** webview收到vscode的回复消息 */
export enum WebviewRspCommand {
  /** 开发问答-codefree回复的消息片段 */
  ANSWER_RECVED = 'answer-recved',
  /** 开发问答-已加载的历史会话内容 */
  CONVERSATION_LOADED = 'conversation-loaded',
  /** 开发问答-会话内容有变更 */
  CONVERSATION_CHANGED = 'conversation-changed',
  /** 开发问答-编辑器中选中代码并右键选择对应菜单发起提问 */
  CODE_SELECTION_ASKED = 'code-selection-asked',
  /** 开发问答-返回当前编辑器已选择代码，请求WebViewReqCommand.RETRIVE_CODE_SELECTION后会触发 */
  RETURN_CODE_SELECTION = 'return-code-selection',
  /** 开发问答-当前编辑器已选择代码有变化 */
  CODE_SELECTION_CHANGED = 'code-selection-changed',

  /** 回复是否已登录codefree结果 */
  CHECK_IF_LOGIN_RESPONSE = 'check-if-login-response',

  PROMPTS_RESPONSE = 'prompts-response',

  SWITCH_CONVERSATION_RESPONSE = 'switch-conversation-response',

  FEEDBACK_CONVERSATION_RESPONSE = 'feedback-conversation-response',

  CONVERSATION_REFRESHED = 'conversation-refreshed',

  CONVERSATION_REMOVED = 'conversation-removed',

  CONVERSATION_ADDED = 'conversation-added',

  PUSH_LOGIN_STATUS_RESPONSE = 'push-login-status-response',

  KNOWLEDGE_BASE_RESPONSE = 'knowledge-base-response',

  CODE_SECURITY_SCAN_RESPONSE = 'code-security-scan-response',

  PUSH_THEME_CHANGED = 'push-theme-changed',

  PUSH_NETWORK_STATUS_RESPONSE = 'push-network-status-response',

  CODE_SECURITY_SCAN_START = 'code-security-scan-start',

  QA_FOR_RELATED_FILES_RESPONSE = 'qa-for-related-files-response',

  FILE_EXCEED_LIMIT = 'file-exceed-limit',
}

export const ResponseRequestMap: any = {
  [WebviewRspCommand.CONVERSATION_LOADED]: WebViewReqCommand.CONVERSATION_LOAD,
  [WebviewRspCommand.CONVERSATION_ADDED]: WebViewReqCommand.CONVERSATION_ADD,
  [WebviewRspCommand.SWITCH_CONVERSATION_RESPONSE]: WebViewReqCommand.CONVERSATION_SWITCH,
  [WebviewRspCommand.CONVERSATION_REMOVED]: WebViewReqCommand.CONVERSATION_REMOVE,
  [WebviewRspCommand.CONVERSATION_CHANGED]: WebViewReqCommand.CONVERSATION_EDIT_TITLE,
  [WebviewRspCommand.ANSWER_RECVED]: WebViewReqCommand.CHAT_REQUEST,
  [WebviewRspCommand.CHECK_IF_LOGIN_RESPONSE]: WebViewReqCommand.CHECK_IF_LOGIN,
  [WebviewRspCommand.RETURN_CODE_SELECTION]: WebViewReqCommand.RETRIVE_CODE_SELECTION,
  [WebviewRspCommand.FEEDBACK_CONVERSATION_RESPONSE]: WebViewReqCommand.CONVERSATION_FEEDBACK,
  [WebviewRspCommand.CONVERSATION_REFRESHED]: WebViewReqCommand.CONVERSATION_REFRESH,
  [WebviewRspCommand.KNOWLEDGE_BASE_RESPONSE]: WebViewReqCommand.KNOWLEDGE_BASE_REQUEST,
  [WebviewRspCommand.PROMPTS_RESPONSE]: WebViewReqCommand.PROMPTS_REQUEST,
  [WebviewRspCommand.CODE_SECURITY_SCAN_RESPONSE]: WebViewReqCommand.CODE_SECURITY_SCAN_REQUEST,
  [WebviewRspCommand.QA_FOR_RELATED_FILES_RESPONSE]: WebViewReqCommand.QA_FOR_RELATED_FILES_REQUEST,
};

export enum CodeSecurityEventType {
  GET_SCAN_FILES = 'getscanfiles',
  RUN_SCAN = 'runscan',
  QUERY_SCAN_ISSUES = 'queryscanissues',
  VIEW_DETAIL = 'viewdetail',
  STOP_SCAN = 'stopscan',
  AI_EXPLAIN = 'aiexplain',
  STOP_AI_REQUEST = 'stopairequest',
}

/** 开发问答回复消息状态码 */
export enum WebViewRspCode {
  /** 成功 */
  SUCCESS = 0,
  /** 未登录 */
  NOT_LOGIN = 1,
  /** 当前的回复是否需要清除，一般用于回复内容出错，需要显示错误信息，清空前面已回复的消息片段 */
  NEED_CLEAR = 2,
}

export enum ChatMessageType {
  // 帮助指引
  HELP = -1,
  /** 解析代码 */
  EXPLAIN = 1,
  /** 单元测试 */
  UNITTEST = 2,
  /** 生成代码注释 */
  COMMENT = 3,
  /** 自然语言编程 */
  MANUAL_GENERATE = 4,
  /** 编程助手问答 */
  CHAT_GENERATE = 5,
  /** 生成代码优化建议 */
  OPTIMIZATION = 6,
  // 异常
  EXCEPTION_FIX = 7,
  // 知识库问答
  KB_ASSISTANT = 8,

  QA_RELATED_FILES = 9,
}

export enum SubServiceType {
  CODECHAT = 'codechat',
  ASSISTANT = 'assistant',
  CODEEXPLAIN = 'codeexplain',
  CODECOMMENT = 'codecomment',
  CODEUNITTEST = 'codeunittest',
  CODEOPTIMIZATION = 'codeoptimize',
  CODEEXCEPTIONFIX = 'fixexception',
  KBASSISTANT = 'kbassistant',
}

export enum SubServiceOfType {
  kbassistant = 8,
  fixexception = 7,
  codeoptimize = 6,
  codeunittest = 2,
  codecomment = 3,
  codeexplain = 1,
  assistant = 5,
  codechat = 4,
}

export const ChatMessageTypeDesc: Record<string, string> = {
  '-1': '帮助指引',
  1: '解释代码',
  2: '生成单元测试',
  3: '生成代码注释',
  4: '自然语言编程',
  5: '编程助手问答',
  6: '生成代码优化建议',
  7: '异常报错解释',
  8: '知识库问答',
  9: '开始聊天'
};

export const ChatMessageTypeEnDesc: Record<string, string> = {
  '-1': '/help',
  1: '/explain code',
  2: '/generate unit test',
  3: '/generate comment',
  4: '/natural language programming',
  5: '/programming assistant',
  6: '/generate optimization',
};

export enum RtnCode {
  SUCCESS = 0,
  NOT_LOGIN = 2,
  SEND_ERROR = 4,
  KB_DELETED = 15, // 知识库被删除
  INVALID = 21,
  CHAT_NETWORK_OFFLINE = 23,
  SENSITIVE_WORD = 13,
  SENSITIVE_WORD2 = 14,
  RECONNECTED_SUCCESS = 5, // 重连
  OFFLINE = 1, //网络异常
  FORBBIDEN_WS = 6, // 禁用
  FORBBIDEN_HTTP = 403, // 禁用
  NOT_EXSIT = 22, // 不存在
}
export enum PromptsEventType {
  GET_TEMPLATES = 'gettemplates',
  OPERATE_TEMPLATE = 'operatetemplate',
  GET_CATEGORIES = 'getcategories',
}
// 最长的问题字符限制
export const MaxQuestionLength = 10000