import * as vscode from 'vscode';
import { SrdCommand } from '../common/constants';
import { getHttpServerHost } from '../utils/envUtil';

function openLink(name: string) {
  // https://www.srdcloud.cn
  const target: any = {
    feedback: '/feedback/feedback',
    help: '/helpcenter/content?id=1189244999559761920',
    keyboard: '/helpcenter/content?id=1189245267638702080&pageIndex=1',
  };
  const path = target[name];
  const uri = `${getHttpServerHost()}${path}`;
  vscode.env.openExternal(vscode.Uri.parse(uri));
}
export function registerSidebarToolbar(context: vscode.ExtensionContext) {
  context.subscriptions.push(
    vscode.commands.registerCommand(SrdCommand.OPEN_FEEDBACK, async () => {
      openLink('feedback');
    }),
    vscode.commands.registerCommand(SrdCommand.OPEN_QUESTION, async () => {
      openLink('help');
    }),
    vscode.commands.registerCommand(SrdCommand.CLOSE_SIDEBAR, async () => {
      vscode.commands.executeCommand('workbench.action.closeSidebar');
    })
  );
}
