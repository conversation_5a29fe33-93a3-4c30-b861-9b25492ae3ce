<template>
  <div v-show="chatStore.promptView === ''" class="app-box">
    <div class="home-nav">
      <a-tabs :active-key="chatStore.tab" style="" @change="(v: any) => {
        chatStore.tab = v as string
      }">
        <a-tab-pane key="chat" title="问 答">
        </a-tab-pane>
        <a-tab-pane key="composer" title="智能编程">
        </a-tab-pane>
      </a-tabs>
      <Toolbar style="flex: 1" @createNewConv="createNewConvByToolbar" />
    </div>

    <div class="app-content">
      <div v-show="chatStore.tab === 'chat'" class="chat-container">
        <ChatBody class="app-body" ref="chatBodyRef" :answering-text="chat.answeringText" @onReGen="onReGen"
          @finish-print="() => { finishPrint() }" @shortcut="onShortCut" @submit-msg="submitMsg"
          @onSelectApi="onSelectApi">
        </ChatBody>
        <Footer class="app-footer" @shortcut="onShortCut" @onSend="onSend">
        </Footer>
      </div>
      <ComposerView v-show="chatStore.tab === 'composer'"></ComposerView>
    </div>
  </div>
  <ConversationPanel v-if="chatStore.promptView === 'conversation'" @select-conv="selectConv"
    @createNewConv="createNewConvByToolbar" />
  <DirectiveTemplatePanel v-if="chatStore.promptView === 'directiveTemplate'" @createNewConv="createNewConvByToolbar" />
</template>

<script lang="ts" setup>
import ComposerView from '@/composer/index.vue'
import Toolbar from './views/Toolbar.vue';
import Footer from '@/views/InputBox.vue';
import ChatBody from '@/views/ChatBody.vue';
import ConversationPanel from '@/views/ConversationPanel.vue';
import { onMounted, reactive, ref, nextTick, watch, getCurrentInstance, computed } from 'vue';
import {
  ChatMessageType,
  ChatMessageTypeDesc,
  RtnCode,
  SubServiceOfType,
  WebViewReqCommand,
  WebviewRspCommand,
  ResponseRequestMap,
  MaxQuestionLength
} from '@/constants/common';
import { ChatBodyData, Conv } from '@/types/index';
import { FileTypeIcon, dateFormat, isForbidden, showForbiddenModal, toast } from '@/utils';
import { useChatStore } from '@/store/chat';
import { Message } from '@arco-design/web-vue';
import DirectiveTemplatePanel from './views/DirectiveTemplatePanel.vue';
import { getSubService } from './utils/common';
import { getFileNameFromPath, getFolderNameFromPath } from '@/utils/index'
import * as sender from '@/MessageSender'
import { useComposer } from '@/store/composer'
import * as MessageStorage from '@/composer/message-storage.js'

const chatStore = useChatStore();
const composer = useComposer()
const chatBodyRef = ref<null | InstanceType<typeof ChatBody>>(null);

/*
answer
code
inValid
isEnd
reqId
*/
const chat = reactive<ChatBodyData>({
  answeringText: '',
  answerCode: 0,
  answerIsEnd: false,
  askType: ChatMessageType.CHAT_GENERATE,
  reqId: '',
  error: ''
});
const app = getCurrentInstance();
const $EventBus = app?.appContext.config.globalProperties.$EventBus;


const relatedFiles = computed(() => {
  return composer.currentSelectedList.filter((item: any) => item.itemType === 'file').map((item: any) => {
    return {
      itemType: 'file',
      text: item.code,
      path: item.path,
      startLine: item.startLine,
      endLine: item.endLine,
    }
  })
})

const selectFiles = computed(() => {
  const path = chatStore.selectedCodeInfo.filePath || chatStore.selectedCodeInfo.path
  const code = chatStore.selectedCodeInfo.code || ''
  if (code && path) {
    return [{
      path,
      itemType: 'file',
      startLine: chatStore.selectedCodeInfo.startLine,
      endLine: chatStore.selectedCodeInfo.endLine,
      text: code
    }]
  }
  return []
})


chatStore.endBy = ''
function createNewConvByToolbar() {
  chatStore.endBy = ''
  composer.currentSelectedList = []
  $EventBus.emit('setFooterInputMessage', {id: '', content: ''});
  chatStore.workItems = []
  if (chatStore.answering && !chatStore.isEnd) {
    chatStore.handleCancelChatRequest();
    $EventBus.emit('onStopAnswer');
    chatStore.endBy = 'createBtn'

  } else {
    createNewConv()
  }

}
/**
 * @description: 新建会话，指定title时，直接创建新会话
 * @param {*} title
 * @return {*}
 */
function createNewConv(title: string = '', isAskingCode = false) {
  chatStore.promptView = ''
  chatStore.helpNotes = ''
  chatStore.isAskingCode = isAskingCode
  chatStore.disabledInputBox = false
  console.log('createNewConv', title, chat)
  chat.answeringText = '';
  chatStore.handleCancelChatRequest();
  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_ADD,
    data: {
      title,
      pageNum: 1
    },
  });
  chatStore.lastTemplateId = ''
  console.log('CONVERSATION_ADD', '新建一个会话')
}


function selectConv(conv: Conv) {
  chatStore.currentLib.id = ''
  chatStore.currentLib.name = ''
  chatStore.currentConv = conv
  chatStore.chatAPIMap = {}
  chatStore.stopPrintPositionMap = {}
  composer.currentSelectedList = []
  $EventBus.emit('setFooterInputMessage', { id: '', content: '' });

  chatStore.workItems = []
  chat.answeringText = '';
  chatStore.isReAskReqId = ''
  console.log('selectConv', conv)
  if (conv.isNewConversation) {
    chatStore.pendingList = []
    chat.answeringText = '';
    chatStore.lastTemplateId = ''
    endJob()
    return
  }
  chooseConversation(conv.id!)
  scrollMessageToBottom()
}

/**
 * 来自快捷菜单的发送消息
 * @param type 
 */
function onShortCut(type: number) {
  const code = chatStore.selectedCodeInfo.code || ''
  if(type === 999) {
    createNewConv()
    chatStore.helpNotes = 'new'
    return
  }
  if (!code && type !== ChatMessageType.HELP) {
    return Message.info('请在编辑器中选择代码');
  }
  composer.currentSelectedList = []
  $EventBus.emit('setFooterInputMessage', { id: '', content: '' });
  chatStore.workItems = []
  if (type === ChatMessageType.HELP) {
    createNewConv()
    loadConversationList();
    chatStore.helpNotes = 'help'
  } else {
    chatStore.helpNotes = ''

    const desc = ChatMessageTypeDesc[type]
    if ([ChatMessageType.OPTIMIZATION, ChatMessageType.COMMENT].includes(type)) {
      chatRequest([desc].filter(item => item).join('\n'), type, {
        relatedFiles: selectFiles.value,
      })
    } else {
      chatRequest([desc].filter(item => item).join('\n'), type)
    }

  }
}

function onSend({ input }: any) {
  submitMsg(input)
}
/**
 * 来自输入框
 * @param msg 
 */
function submitMsg(msg: string) {

  // const code = chatStore.selectedCodeInfo.code || ''
  let question = msg
  const list = relatedFiles.value.slice()
  for (const file of selectFiles.value) {
    if (list.find((item: any) => item.path === file.path && item.startLine === file.startLine && item.endLine === file.endLine)) {
      continue
    }
    list.push(file)
  }
  if (chatStore.currentLib.id !== '') {
    chatRequest(question, ChatMessageType.KB_ASSISTANT, { relatedFiles: list })
  } else {
    const isRelatedFileType = !!composer.currentSelectedList.find((f: any) => f.type === ChatMessageType.QA_RELATED_FILES)

    if (isRelatedFileType) {

      chatRequest(question, ChatMessageType.QA_RELATED_FILES, {
        relatedFiles: list
      })
    } else {
      chatRequest(question, ChatMessageType.CHAT_GENERATE, { relatedFiles: list })
    }
  }
}

function onSelectApi(quote: any) {
  chatRequest(quote.page_content, ChatMessageType.KB_ASSISTANT, { quote: [quote] })
  chatStore.lastQuote = [quote]
}
function truncateText(code: string, question: string, maxLength = MaxQuestionLength) {
  let isTruncated = false;
  let text = ''

  if (question.length >= MaxQuestionLength) {
    isTruncated = true;
    text = question.slice(0, MaxQuestionLength)
    console.log('truncateText-1')

  } else if ([code, question].join('\n').length <= MaxQuestionLength) {
    text = [code, question].join('\n')
    console.log('truncateText-2')
  } else if (question.length < MaxQuestionLength) {
    if (code) {
      if (code.length + question.length + 1 > MaxQuestionLength) {
        isTruncated = true
        let code1 = code.slice(0, MaxQuestionLength - question.length - '\n'.length - '```'.length) + '\n' + '```'
        text = [code1, question].join('\n')
        console.log('truncateText-3')

      }
    } else {
      text = question
      console.log('truncateText-5')
    }
  }

  if (isTruncated) {
    Message.info('所选代码超长，已截断');
  }

  return text
}

function getWorkItems() {

  return composer.currentSelectedList.slice().filter((item: any) => {
    return item.itemType === 'workItem'
  }).map((item: any) => {
    return {
      workItemKey: item.workItemKey,
      description: item.description,
      title: item.title
    }
  })

}
function getContextInputItems(relatedFiles: any[]) {
  const contextInputItems: any[] = []
  const list = composer.currentSelectedList.slice()
  const contents: any[] = []

  console.log('getContextInputItems', list)
  console.log('relatedFiles', relatedFiles)

  relatedFiles.forEach((file: any) =>{
    if(!list.find((item: any)=>{
      return item.itemType === 'file' && item.path === file.path && item.startLine === file.startLine && item.endLine === file.endLine
    })) {
      list.push({...file, itemType: 'file'})
    }
  })

  list.forEach((mItem: any) => {
    const data = mItem
    let contextInputItem = null
    let content = null
    if (mItem.itemType === 'project') {
      contextInputItem = {
        itemTag: "codebase",
        itemValue: ""
      }
      content = {
        type: 'codebase',
        name: '当前工程',
        icon: FileTypeIcon.codebase
      }
    } else if (mItem.itemType === 'file') {
      let path = data.path
      if(typeof mItem.startLine === 'number' && typeof mItem.endLine === 'number'){
        path +=` (${mItem.startLine}-${mItem.endLine})`
      }
   
      contextInputItem = {
        itemTag: "file",
        itemValue: path
      }
      const name = getFileNameFromPath(data.path)
      const type = name.split('.')[name.split('.').length - 1]

      content = {
        type: 'file',
        name,
        path: data.path,
        startLine: mItem.startLine,
        endLine: mItem.endLine,
        icon: FileTypeIcon[type] || FileTypeIcon.file
      }
    } else if (mItem.itemType === 'dir') {
      contextInputItem = {
        itemTag: "folder",
        itemValue: data.path
      }
      const name = getFolderNameFromPath(data.path)
      content = {
        name,
        type: 'folder',
        path: data.path,
        icon: FileTypeIcon.folder
      }
    }
    if (contextInputItem)
      contextInputItems.push(contextInputItem)
    if (content) {
      contents.push(content)
    }
  })
  return { contextInputItems, contents }
}

/**
 * 发送提问
 * @param question 
 * @param type 
 */
function chatRequest(question: string, type = ChatMessageType.CHAT_GENERATE, extraData: any = {}) {

  console.log("chatStore.selectedCodeInfo.code in chatRequest", chatStore.selectedCodeInfo.code)
  chatBodyRef.value?.setWillScrollTobottom(true)
  chatStore.printIndex = 0
  chatStore.endBy = ''
  chatStore.lastQuote = []
  chatStore.helpNotes = ''
  // 不允许发送空消息
  if (!question.replace('\n', '').trim()) return
  const subService = getSubService(type)
  const createTime = dateFormat(new Date())
  const parentReqId = chatStore.lastParentReqId


  // 发送消息
  const msgData = {
    command: WebViewReqCommand.CHAT_REQUEST,
    data: {
      question,
      createTime,
      type,
      parentReqId,
      subService,
      templateId: chatStore.lastTemplateId,
      chatContext: chatStore.getChatContext(),
      kbId: chatStore.currentLib.id !== '' ? +chatStore.currentLib.id : '',
      relatedFiles: [],
      contextInputItems: [],
      selectedWorkItems: getWorkItems(),
      ...extraData,

    }
  }
  // 重构一下这个全数组
  const { contents, contextInputItems } = getContextInputItems(msgData.data.relatedFiles)
  msgData.data.contextInputItems = contextInputItems
  
 

  if (!msgData.data.quote) {
    let code = chatStore.selectedCodeInfo.code || ''
    msgData.data.question = truncateText(code, msgData.data.question)
  }

  console.log('currentconv', chatStore.currentConv)
  chatStore.setSubService(subService)
  chatStore.pendingList = [
    {
      role: 'user',
      msg: msgData.data.question,
      files: [...contents, ...msgData.data.selectedWorkItems.map((item: any) => {
        return {
          ...item,
          type: 'workItem',
          name: item.workItemKey,
          path: '',
          icon: FileTypeIcon.workItem
        }
      })]
    }
  ]

  chatStore.stopChatParam = {
    reqId: chatStore.lastParentReqId,
    parentReqId: chatStore.lastParentReqId,
    question,
    questionType: 'newAsk'
  }
  console.log('提问', msgData)
  sender.postMessage(msgData);
  chatStore.answering = true
  chat.answeringText = ''
  chatStore.setLastAnswer('')
  scrollMessageToBottom();
  chatStore.isReAskReqId = ''

}

// 切换会话时候，清空异常临时节点
watch(() => chatStore.currentConv.id, () => {
  chatStore.lastParentReqId = ''
}, { immediate: true })

function onReGen(item: any) {
  console.log(item)
  const question = item.content[0].text;
  const type = SubServiceOfType[item.subService]
  console.log('onReGen', item)
  // 不允许发送空消息
  if (!question.replace('\n', '').trim()) return
  const subService = item.subService
  const createTime = dateFormat(new Date())
  const parentReqId = item.reqId

  // 发送消息
  const msgData: any = {
    command: WebViewReqCommand.CHAT_REQUEST,
    data: {
      question,
      createTime,
      type,
      parentReqId,
      subService,
      templateId: chatStore.lastTemplateId,
      chatContext: chatStore.getChatContext(),
      questionAskType: 'reAsk',
      kbId: chatStore.currentLib.id !== '' ? +chatStore.currentLib.id : '',
      relatedFiles: item.content.filter((f: any) => f.type === "local_file").map((f: any) => {
        const file = f.local_file || {}
        return {
          ...file,
          text: f.text
        }
      }),

    }
  }


  chatStore.setSubService(subService)

  chatStore.stopChatParam = {
    reqId: parentReqId,
    parentReqId: parentReqId,
    question,
    questionType: 'reAsk'
  }
  sender.postMessage(msgData);
  console.log('重新回答', msgData)

  chatStore.isReAskReqId = item.reqId

  chatStore.answering = true
  chatStore.setLastAnswer('')
  scrollMessageToBottom();
}


function _listener(e: any) {
  const payload = e.data;
  if (!([
    WebviewRspCommand.ANSWER_RECVED,
    WebviewRspCommand.RETURN_CODE_SELECTION].includes(payload.command))
  ) {
    const timeEndKey = `postMessage[${ResponseRequestMap[payload.command]}]`
    // console.timeEnd(timeEndKey)
    // console.log('_listener', payload)

  }

  switch (payload.command) {
    case WebviewRspCommand.RETURN_CODE_SELECTION:
      chatStore.selectedCodeInfo = {
        ...payload.data,
        code: (payload.data.code || '').slice(0, chatStore.codeSizeLimit)
      }
      break;
    case WebviewRspCommand.PUSH_THEME_CHANGED:
      console.log('PUSH_THEME_CHANGED', payload)
      onThemeChanged(payload)

      break;
    // 刷新会话列表
    case WebviewRspCommand.CONVERSATION_REFRESHED:
      console.log('CONVERSATION_REFRESHED', payload)
      if (isForbidden(payload)) {
        showForbiddenModal(sender)
        return
      }
      onLoadConversationList(payload)

      break;
    // 拉dialog
    case WebviewRspCommand.SWITCH_CONVERSATION_RESPONSE:
      console.log('SWITCH_CONVERSATION_RESPONSE', payload)
      console.log('FFFF', 3)

      console.log('chatStore.endBy', chatStore.endBy)
      if (chatStore.endBy === 'selectionAsked') {
        break
      }

      if (payload.data.code === RtnCode.SUCCESS && payload.data.data && payload.data.data.dialog) {
        chatStore.pendingList = []
        chat.answeringText = '';
        chatStore.lastTemplateId = ''
        endJob()
        try {

          const dialog = payload.data.data.dialog
          chatStore.currentConv.questions = dialog.questions
          chatStore.setDialog(dialog.systemPrompt, dialog.questions.subService)
          if (dialog.modelRouteCondition && dialog.modelRouteCondition.payload) {
            chatStore.lastTemplateId = dialog.modelRouteCondition.payload.templateId
          }
          chatStore.isReAskReqId = ''
          chatStore.disabledInputBox = false
          // setTimeout(async () => {
          
          //   const els = document.querySelectorAll('.mermaid')
          //   if (els && els.length) {
          //     mermaid.init(undefined, els as any);
          //   }

          // }, 600)

        } catch (err) {
          console.log('SWITCH_CONVERSATION_RESPONSE ERROR', err)
        }
      } else if (payload.data.code === RtnCode.NOT_EXSIT) {
        chat.answeringText = ''
        chatStore.lastTemplateId = ''
        endJob()
      }else  {
        chatStore.lastTemplateId = ''
        endJob()
        chatStore.pendingList.push({
          role: 'assistant',
          msg: payload.data.error || '网络异常，请稍后再试。',
        })
        chat.answeringText = ''

      }



      break;
    case WebviewRspCommand.PUSH_LOGIN_STATUS_RESPONSE:
      console.log('PUSH_LOGIN_STATUS_RESPONSE', payload)
      onStatusChange(payload)
      break;
    case WebviewRspCommand.PUSH_NETWORK_STATUS_RESPONSE:
      onNetworkStatusChange(payload)

      break;
    case WebviewRspCommand.CONVERSATION_REMOVED:
      console.log('CONVERSATION_REMOVED', payload)
      onConversationRemoved(payload)
      break;
    case WebviewRspCommand.CONVERSATION_CHANGED:
      console.log('CONVERSATION_CHANGED', payload)
      if (payload.data.code !== 0) {
        toast(payload.data.error, 'top')
      }
      loadConversationList()
      break;
    case WebviewRspCommand.CONVERSATION_ADDED:
      console.log('CONVERSATION_ADDED', payload)
      selectConv(payload.data.conversations[0])
      loadConversationList()
      console.log('FFFF', 5)


      break;
    case WebviewRspCommand.ANSWER_RECVED:
      console.log('ANSWER_RECVED', payload)
      onResponseAnswer(payload)
      break;
    case WebviewRspCommand.CODE_SELECTION_ASKED:
      console.log('CODE_SELECTION_ASKED', payload)
      // 收到选择代码的提问（代码解释、单元测试、代码注释） => 新建会话 => 发起提问
      const code = (payload.data.code || '').slice(0, chatStore.codeSizeLimit)
      const type = payload.data.type || ChatMessageType.CHAT_GENERATE
      // 保存当前的selectedCodeInfo
      const currentSelectedCodeInfo = { ...chatStore.selectedCodeInfo }

      if (ChatMessageType.QA_RELATED_FILES === type) {
        composer.currentSelectedList = composer.currentSelectedList.filter((f: any) => {
          if (f.path === payload.data.filePath &&
            f.startLine === payload.data.startLine &&
            f.endLine === payload.data.endLine
          ) {
            return false
          }
          return true
        })
        composer.currentSelectedList.push({
          code,
          type,
          itemType: 'file',
          path: payload.data.filePath,
          name: getFileNameFromPath(payload.data.filePath),
          size: 1,
          startLine: payload.data.startLine,
          endLine: payload.data.endLine,
        })

      } else {
        chatStore.tab = 'chat'
        console.log('FFFF', '---')

        console.log('FFFF', 1)
        createNewConv('', true)

        setTimeout(() => {
          console.log('FFFF', 2)

          chatStore.endBy = 'selectionAsked'
          const desc = ChatMessageTypeDesc[type]
          const errDesc = payload.data.errDesc

          chatStore.selectedCodeInfo = {
            ...payload.data,
            code: (payload.data.code || '').slice(0, chatStore.codeSizeLimit)
          }

          if ([ChatMessageType.OPTIMIZATION, ChatMessageType.COMMENT].includes(type)) {
            chatRequest([desc, errDesc].filter(item => item).join('\n'), type, {
              relatedFiles: [{
                path: payload.data.filePath,
                startLine: payload.data.startLine,
                endLine: payload.data.endLine,
                text: code
              }]
            })
          } else {
            // 在调用chatRequest之前恢复selectedCodeInfo
            chatStore.selectedCodeInfo = currentSelectedCodeInfo
            console.log("chatStore.selectedCodeInfo.code before chatRequest", chatStore.selectedCodeInfo.code)
            chatRequest([desc, errDesc].filter(item => item).join('\n'), type)
          }
        }, 500)
      }



      break;
    case WebviewRspCommand.CODE_SELECTION_CHANGED:
      console.log('CODE_SELECTION_CHANGED', payload)
      chatStore.selectedCodeInfo = {
        ...payload.data,
        code: (payload.data.code || '').slice(0, chatStore.codeSizeLimit)
      }

      break;
    case WebviewRspCommand.KNOWLEDGE_BASE_RESPONSE:
      console.log(payload)
      onResponseLib(payload)
      break;
    case WebviewRspCommand.CHECK_IF_LOGIN_RESPONSE:
      onCheckIfLoginStatus(payload)
      break;
    case WebviewRspCommand.QA_FOR_RELATED_FILES_RESPONSE:
      onRelatedFilesResponse(payload)
      break;
    case WebviewRspCommand.FILE_EXCEED_LIMIT:
      Message.info('提问超出长度，codefree只阅读了部分内容')
      break
    default:
      break;
  }
}


function onLoadConversationList(payload: any) {
  if (payload.data.code === RtnCode.SUCCESS) {
    chatStore.conversations = payload.data.conversations || []
    chatStore.historyOnline = true
  } else {
    chatStore.conversations = []
  }
}

function onThemeChanged(payload: any) {
  const theme = (payload.data.theme || '').toLowerCase()
  document.body.setAttribute('arco-theme', theme.includes('light') ? 'white' : 'dark')
}
// 重连 断网
function onNetworkStatusChange(payload: any) {
  if (payload.data.code === RtnCode.RECONNECTED_SUCCESS) {
    chatStore.disabledInputBox = false
    chatStore.online = true
    chatStore.handleCancelChatRequest()
    chat.answeringText = ''
    chatStore.answering = false
    // // 处于登录状态
    if (chatStore.codeFreeLoginStatus) {
      chatStore.answering = false
    } else {
      createNewConv()
    }
  } else if (payload.data.code === RtnCode.OFFLINE) {
    chatStore.online = false
    chatStore.disabledInputBox = true
  }
}

function onConversationRemoved(payload: any) {
  chatStore.conversations = payload.data.conversations || []
  if (chatStore.conversations.length) {
    chatStore.handleSetCurrentConv(chatStore.conversations[0])
    selectConv(chatStore.conversations[0])
  } else {
    createNewConv()
  }

}

// 登录登录、
function onStatusChange(payload: any) {
  if (payload.data.config) {
    const projectPath = payload.data.config.projectPath
    if(!chatStore.config.projectPath || projectPath !== chatStore.config.projectPath) {
      MessageStorage.setProjectPath(projectPath)
      composer.loadMessageList()
    }
    chatStore.config = Object.assign(chatStore.config, payload.data.config)
    if (payload.data.config.host) {
      chatStore.host = payload.data.config.host
    }
  }
  if (payload.data.code === RtnCode.SUCCESS) {
    chatStore.online = true
    chatStore.codeFreeLoginStatus = true
    // 登录了
    if (!chatStore.currentConv.id) {
      createNewConv()
    } else {
      loadConversationList()
    }
  } else {
    chatStore.codeFreeLoginStatus = false
    composer.diffList = []
    if (payload.data.error) {
      Message.info(payload.data.error);
    }
    createNewConv()
  }
}

function onCheckIfLoginStatus(payload: any) {
  if (payload.data.code === RtnCode.SUCCESS) {
    chatStore.codeFreeLoginStatus = true
    // 登录了
    if (!chatStore.currentConv.id) {
      createNewConv()
    } else {
      loadConversationList()
    }
  } else {
    chatStore.codeFreeLoginStatus = false
    composer.diffList = []

  }

}


// 知识库列表
function onResponseLib(payload: any) {
  const { type, data } = payload.data
  if (type === "searchdevkbs") {
    if (data.code !== 0) return
    chatStore.libs = data && (data.data || [])
  } else if (type === 'getkbinfo') {
    if (data.code === 0) {
      chatStore.currentLib.name = data.data.kbName
      console.log(data.data.kbName)
    }
  }
}


function onRelatedFilesResponse(payload: any) {
  if (payload.data.code === RtnCode.SUCCESS) {
    if (payload.data.reqType === 'getrelatedfiles') {

      chatStore.recentlyUsedFiles = Array.isArray(payload.data.recentlyUsedFiles) ? payload.data.recentlyUsedFiles.map((item: any, index: number) => {
        return {
          ...item,
          isCurrentFile: payload.data.currentIndex === index
        }
      }) : []
      chatStore.fileTree = payload.data.fileTree ? [payload.data.fileTree] : []
    }
  }
}

function onResponseAnswer(payload: any) {
  chatStore.lastIsEndAnswerReqId = ''

  // 收到AI的回答
  const answer = (payload.data || {}) as {
    answer?: string;
    isEnd: boolean;
    code?: RtnCode;
    error?: string;
    reqId: string;
    quote?: any
  };
  chat.answerCode = answer.code
  chat.answerIsEnd = answer.isEnd
  chat.error = answer.error
  chat.reqId = answer.reqId
  chatStore.stopChatParam.reqId = answer.reqId
  if (!chat.answeringText) {
    chatBodyRef.value?.setWillScrollTobottom(true)
  }

  if (answer.code === RtnCode.SUCCESS) {
    // 成功
    chat.answeringText += (answer.answer || '');
    chatStore.setLastAnswer(chat.answeringText)
    // if (!chat.answerIsEnd) {
    //   chatStore.answering = true
    // }

  } else if (answer.code === RtnCode.NOT_LOGIN) {
    chatStore.codeFreeLoginStatus = false
    composer.diffList = []

  }
  chatStore.lastIsEndAnswerReqId = answer.reqId
  chatStore.isEnd = false

  // 会话结束
  if (answer.isEnd) {
    console.log('FFFF', 4)

    chatStore.isEnd = true
    console.log('收到 回答 isEnd')
    if (answer.quote && answer.quote.api) {
      chatStore.chatAPIMap[chat.reqId] = true
    }
    if (chatStore.endBy === 'createBtn') {
      const timeEndKey = `postMessage[${WebViewReqCommand.STOP_CHAT_REQUEST}]`
      // console.timeEnd(timeEndKey)
      createNewConv()
      chatStore.endBy = ''
    } else if (chatStore.endBy === 'stopBtn') {
      const timeEndKey = `postMessage[${WebViewReqCommand.STOP_CHAT_REQUEST}]`
      // console.timeEnd(timeEndKey)
      finishPrint()
      chatStore.endBy = ''
    } else if (chatStore.endBy === 'selectionAsked') {

      chatStore.endBy = ''
    } else if (chatStore.endBy === '') {
      if (chatStore.printIndex >= chat.answeringText.length) {
        finishPrint()
      }
    }

  }
  scrollMessageToBottom()

  if (isForbidden(payload)) {
    showForbiddenModal(sender)
    return
  }
}


function loadConversationList() {
  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_REFRESH,
    data: {
      pageNum: 1
    }
  })
  loadLibList()
}

// 加载知识库列表
function loadLibList() {
  console.log('searchdevkbs')
  sender.postMessage({
    command: WebViewReqCommand.KNOWLEDGE_BASE_REQUEST,
    data: {
      reqType: 'searchdevkbs',
      pageSize: 1000
    }
  })
}


function chooseConversation(dialogId: string) {
  console.log('FFFF', 6)

  console.log('chooseConversation', dialogId)

  sender.postMessage({
    command: WebViewReqCommand.CONVERSATION_SWITCH,
    data: {
      dialogId
    },
  });
}
function scrollMessageToBottom() {
  nextTick(() => {
    chatBodyRef.value?.scrollToBottom();
  });
}
const endJob = () => {
  chatStore.answering = false
  scrollMessageToBottom()
  chat.error = ''
}

/**
 * @description: 开发问答问题回复完成，结束会话的打印机效果
 * @return {*}
 */
function finishPrint() {
  scrollMessageToBottom()
  nextTick(() => {
    if (chat.answerIsEnd) {
      // 上报生成的代码行数信息
      const list = `${chat.answeringText}`.match(
        /```(.|\r\n|(?<!\r\n)\n(?!\r\n)|(?<!\r\n|\n)\r(?!\r\n|\n))*?```/g
      );
      const resList: string[] = [];
      list?.forEach(item => {
        const str = (item || '').replace(/```/g, '');
        resList.push(str);
      });
      chatStore.handleChatGenReportData(resList.join(''));
      // 未登录
      if (chat.answerCode === RtnCode.NOT_LOGIN) {
        chatStore.codeFreeLoginStatus = false
        composer.diffList = []
        return
      }
      loadConversationList()

      const handler = () => {
        if ([
          RtnCode.SUCCESS,
          RtnCode.SENSITIVE_WORD, // 13
          RtnCode.SENSITIVE_WORD2, // 14
          RtnCode.KB_DELETED
        ].includes(chat.answerCode!)) {
          chooseConversation(chatStore.currentConv.id!)
          return
        }
        chatStore.pendingList.push({
          role: 'assistant',
          msg: chat.error,
          code: chat.answerCode,
          files: []
        })
        endJob()

      }

      setTimeout(handler, 600)

    }
  });
}

function checkIfLogin() {
  if (chatStore.codeFreeLoginStatus) return
  sender.postMessage({
    command: WebViewReqCommand.CHECK_IF_LOGIN,
    data: {}
  })

}

onMounted(() => {
  // 初始化加载历史会话
  // 监控来自vscode的消d
  window.addEventListener('message', _listener);
  createNewConv()
  setInterval(() => {
    checkIfLogin()
  }, 1000)
  if (!sender.isJet) {
    setInterval(() => {
      chatStore.handleRetriveCodeSelection()
    }, 2000)
  }

  sender.postMessage({
    command: WebViewReqCommand.WEBVIEW_LOADED,
    data: {}
  });


    // 在你的 Webview HTML 文件中
  document.addEventListener('copy', copyOrCut);

  document.addEventListener('cut', copyOrCut);
});

function copyOrCut(e: any)  {
  console.log('复制操作被触发', e);
  // 检查选区是否在 div.test 元素内
  const waitForCopyBoxes = document.querySelectorAll('.wait-for-copy-box')!;
  for(const box of Array.from(waitForCopyBoxes) as any[]) {
    if(box.contains(e.target)) {
      console.log('复制内容在 wait-for-copy-box 元素内');
      const selection = window.getSelection();
      if(!selection) return
      // 返回选中的纯文本
      const txt = selection.toString();
      if(chatStore.tab === 'chat') {
        chatStore.handleReportData(txt)
      } else if(chatStore.tab === 'composer') {
        composer.handleReportData(txt)
      }
      return
    }

  }
}
</script>

<style lang="scss">
.app-box {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  color: var(--vscode-foreground);
}

.app-header {}

.app-body {
  flex: 1;
}

.app-footer {}

#app {
  box-sizing: border-box;
  // border-top: 1px solid var(--vscode-panel-border);
}

.app-content {
  flex: 1;
  overflow: hidden;
}

.home-nav {
  display: flex;
  border-bottom: 1px solid var(--vscode-panel-border);
  padding: 0 20px;

  .arco-tabs-content {
    padding-top: 0 !important;
  }

  .arco-tabs-nav:before {
    background-color: inherit !important;

  }

  .arco-tabs-tab {
    margin: 0 0px !important;
    margin-right: 32px !important;
    color: var(--vscode-foreground);



    &:hover {
      color: var(--vscode-foreground) !important;
    }

    .arco-tabs-tab-title {
      &::before {
        background-color: transparent !important;
      }

      &:hover {
        color: var(--vscode-input-foreground) !important;
      }
    }

    .arco-icon {
      color: var(--vscode-input-border) !important;
    }

    &.arco-tabs-tab-active {}

  }
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
