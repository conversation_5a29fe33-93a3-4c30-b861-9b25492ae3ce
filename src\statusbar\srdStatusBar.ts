import { Disposable, ExtensionContext, commands } from 'vscode';
import SrdStatusBarItem from './srdStatusBarItem';
import { StatusBarState, IStatusBarHandler } from './types';
import { CodeCompleteStatus, RtnCode, SecretStorageKey, SrdCommand } from '../common/constants';
import { IServiceObserver, EventType, IServiceObservable } from '../service/types/login';
import { CodeCompleteEnginInst } from '../codecomplete/codeCompleteEngin';
import AutoCompleteStatusStore from './autoCompleteStatusStore';
import { isRunInCloudIDE } from '../adapter/common';
import { AgentManager } from '../agent/agentmanager';
import { AgentStatus } from '../common/constants';

export default class SrdStatusBar implements Disposable, IServiceObserver, IStatusBarHandler {
  private item: SrdStatusBarItem;

  private currentState: StatusBarState = StatusBarState.NotLogin;

  private context: ExtensionContext;

  private observable: IServiceObservable | undefined;

  private disposable: Disposable | undefined;

  public constructor(context: ExtensionContext) {
    this.context = context;
    this.item = new SrdStatusBarItem();
    CodeCompleteEnginInst.setStatusBarHandler(this);

    if (!isRunInCloudIDE()) {
      this.disposable = Disposable.from(this.onSecretChange());
    }

    const agentManager = AgentManager.getInstance();
    agentManager.registerStatusBarHandler(this);
  }

  public setObservable(observable: IServiceObservable): void {
    this.observable = observable;
    this.observable.registerObserver(this);
  }

  public dispose() {
    this.item.dispose();
    this.disposable?.dispose();
    this.observable?.unregisterObserver(this);
    CodeCompleteEnginInst.setStatusBarHandler(null);
  }

  /**
   * 初始化状态栏
   */
  public async init() {
    this.setStatusBarView();
  }

  /**
   * 修改启用或禁用状态
   * @param enabled
   */
  public async setIfEnabled(enabled: boolean) {
    if (enabled && this.currentState !== StatusBarState.CodeCompleteEnabled) {
      this.switchState(StatusBarState.CodeCompleteEnabled);
      this.setStatusBarView();
    } else if (!enabled && this.currentState !== StatusBarState.CodeCompleteDisabled) {
      this.switchState(StatusBarState.CodeCompleteDisabled);
      this.setStatusBarView();
    }
  }
  
  /**
   * 监听登录状态变化
   * @param eventType
   * @param data
   */
  public async onServiceChanged(eventType: EventType, data: unknown) {
    switch (eventType) {
      case EventType.LOGIN:
        this.switchState(StatusBarState.Logining);
        this.setStatusBarView();
        break;
      case EventType.LOGIN_SUCCESS:
        this.switchEnableOrDisableCodeComplete();
        break;
      case EventType.LOGIN_EXPIRED:
      case EventType.LOGIN_CANCELED:
      case EventType.LOGIN_FAILED:
      case EventType.LOGOUT:
        this.switchState(StatusBarState.NotLogin);
        this.setStatusBarView();
        break;
      case EventType.WSSERVER_ERROR: {
        const code = data as number;
        this.switchState(StatusBarState.WSServerError);
        this.setStatusBarView(code);
        break;
      }
      case EventType.WSSERVER_RECONNECT: { 
        const code = data as number;
        this.onCodeChatStatusChange(code);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 监听代码补全状态变化
   * @param status
   * @param data
   */
  public onCodeCompleteStatusChange(status: number, data?: unknown): void {
    switch (status) {
      case CodeCompleteStatus.START: {
        const isAuto = data as boolean;
        if (isAuto) {
          this.switchState(StatusBarState.WaitingAutoComplete);
        } else {
          this.switchState(StatusBarState.WaitingManualComplete);
        }
        this.setStatusBarView();
        break;
      }
      case CodeCompleteStatus.END:
        this.switchEnableOrDisableCodeComplete();
        break;
      case CodeCompleteStatus.ERROR:
        this.switchErrorStatus(data as number);
        break;
      default:
        break;
    }
  }

  /**p
   * 监听CodeChat异常状态变化
   * @param status
   * @param data
   */
  public onCodeChatStatusChange(code: number): void {
    if (code === RtnCode.SUCCESS) {
      this.switchEnableOrDisableCodeComplete();
    } else {
      this.switchErrorStatus(code);
    }
  }

  /**
   * 临听Secret变化，同步多窗口状态栏"自动补全"状态变化
   */
  private onSecretChange() {
    return this.context.secrets.onDidChange(async e => {
      if (e.key === SecretStorageKey.CODECOMPLETE_AUTO_ENABLED) {
        const enabled = await AutoCompleteStatusStore.checkIfEnabled();

        if (this.currentState === StatusBarState.CodeCompleteEnabled && !enabled) {
          this.setIfEnabled(false);
        } else if (this.currentState === StatusBarState.CodeCompleteDisabled && enabled) {
          this.setIfEnabled(true);
        }
      }
    });
  }

  /**
   * 切换状态
   * @param state
   */
  private switchState(state: StatusBarState) {
    this.currentState = state;
  }

  /**
   * 切换自动补全状态
   */
  private async switchEnableOrDisableCodeComplete() {
    const enabled = await AutoCompleteStatusStore.checkIfEnabled();
    this.setIfEnabled(enabled);
  }

  /**
   * 切换服务异常状态
   * @param code
   */
  private switchErrorStatus(code: number) {
    switch (code) {
      case RtnCode.NOT_LOGIN:
        this.switchState(StatusBarState.NotLogin);
        this.setStatusBarView();
        this.observable?.handleEvent(EventType.QUESTION_NOT_LOGIN, code);
        break;
      case RtnCode.INVALID_USER:
      case RtnCode.USER_FORBIDDEN:
        this.switchState(StatusBarState.WSServerError);
        this.setStatusBarView(code);
        commands.executeCommand(SrdCommand.SHOW_SVR_ERROR, code);
        break;
      case RtnCode.INVALID_SESSION_ID:
      case RtnCode.LOGOUT:
      case RtnCode.CANCEL:
        // do nothing
        break;
      default:
        this.switchState(StatusBarState.WSServerError);
        this.setStatusBarView(code);
        break;
    }
  }

  /**
   * 设置状态栏视图
   */
  private setStatusBarView(code?: number) {
    this.item.setStatusView(this.currentState, code);
  }

  /**
   * 处理Agent状态变化
   * @param status Agent状态
   * @param data 额外数据，可能是错误信息或进度
   */
  public onAgentStatusChange(status: number, data?: unknown): void {
    switch (status) {
      case AgentStatus.SYNCING:
        this.switchState(StatusBarState.SyncingAgent);
        this.setStatusBarView();
        break;
      case AgentStatus.SYNC_COMPLETED:
        // 根据登录和启用状态返回之前的状态
        this.switchEnableOrDisableCodeComplete();
        break;
      case AgentStatus.SYNC_FAILED:
        this.switchState(StatusBarState.AgentSyncFailed);
        this.setStatusBarView();
        break;
      case AgentStatus.STARTUP_FAILED:
        this.switchState(StatusBarState.AgentStartupFailed);
        this.setStatusBarView();
        break;
      case AgentStatus.INDEXING:
        this.switchState(StatusBarState.CodeIndexing);
        // 传递索引进度百分比
        this.setStatusBarView(data as number);
        break;
      default:
        break;
    }
  }
}
