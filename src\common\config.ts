/**
 * 全局配置文件
 */

// https超时时间
export const HTTP_TIMEOUT = 30000;

// export const HTTP_TIMEOUT = 600000;

// oauth2请求认证url地址
export const OAUTH2_AUTH_PATH = '/login/oauth/authorize';

// oauth2请求access_token的接口路径
export const OAUTH2_TOKEN_PATH = '/login/oauth/access_token';

// oauth2登录重定向url地址
export const OAUTH2_REDIRECT_PATH = '/login/oauth-srdcloud-redirect';

// oauth2登录唤起vscode的uri路径，一定是<publisher>.<extension name>/xxxxx
export const OAUTH2_CODE_URI = process.env.ISSEC !== 'false' ? 'Secidea.Secidea/did-authenticate' : 'srdcloud.srd-copilot-vscode/did-authenticate';

// 获取用户信息的接口路径
export const USER_INFO_PATH = '/api/usercenterbackend/users/v1/get-user-front';

// 获取最新版本的接口路径
export const LATEST_VERSION_PATH = '/api/composqback/component/v1/detail';

// 用户未申请的错误页面url
export const NO_SUBSCRIBE_PATH = '/srv-error/nosubscribe';

// 用户被禁用的错误页面url
export const FORBIDDEN_PATH = '/srv-error/forbidden';

// 最新版本下载页面url
export const VERSION_DOWNLOAD_PATH = '/composq/copilot';

// 帮助文档页面url
export const HELPDOC_PATH = '/helpcenter/content?id=1189244999559761920';

// 组件广场componentId
export const COMPOSQ_COMPONENT_ID = '39999';

// ws默认路径
export const WS_PATH = '/websocket/peerAppgw';

// ws注册的appGid
export const WS_APP_GID = 'aicode';

// CodeAI请求通道类型: 1-ws, 2-https
export const CHANNEL_TYPE = 1;

export const CLIENT_ANSWER_MODE = 'sync';
export const SERVER_ANSWER_MODE = 'async';

// ws是否需要发送客户端心跳
export const NEED_WS_CLIENT_HEARTBEAT = true;

// ws心跳间隔
export const WS_HERRTBEAT_TIMEOUT = 10000;

// ws重连间隔
export const WS_RECONNECT_INTERVAL = 10000;
export const WS_RECONNENT_LIMIT = 40;

// questionTask请求超时时长
export const ASYNC_MSG_RECV_TIMEOUT = 60000;
export const SYNC_MSG_RECV_TIMEOUT = 60000;

// 最大token限制
export const MAX_NEW_TOKENS_AI_AUTO = 256;
export const MAX_NEW_TOKENS_AI_MANUAL = 512;
export const MAX_NEW_TOKENS_CHAT = 4096;

// prefix和suffer的总长度限制
export const MAX_PROMPT_SIZE = 13000;

// 跨文件关联代码补全LRU缓存长度
export const LRU_CACHE_SIZE = 4

// 代码补全防抖间隔
export const CODE_GEN_DEBOUNCE_DELAY = 300;
export const DELAY_FOR_CODE_ACTION_PROVIDER = 800;

// chat会话存储的文件名
export const CONVERSATION_FILE_PATH = 'srd-copilot-conversation';
export const CONVERSATION_LIMIT = 50;
export const CONVERSATION_TOP = 30;

// 支持InteractiveEditor版本
export const SUPPORT_INTERACTIVE_VERSION = '1.86.0';

// Secidea品牌的name和publisher(name和publisher字段来自package.json, 目前仅用于打开设置页面)
export const SECIDEA_PUBLISHER = 'Secidea';
export const SECIDEA_EXTENSION_NAME = 'Secidea';
export const SECIDEA_SETTINGS_ITEM = `@ext:${SECIDEA_PUBLISHER}.${SECIDEA_EXTENSION_NAME}`;

// 换行符
export const END_OF_LINE = '\n';

// 选择文件+选择代码片段的最大长度为50000个字符
export const MAX_TOTAL_CHARS = 50000;

// 文件上传地址
export const UPLOAD_FILE_PATH = '/api/acbackend/fileupservice/v1/upload';
export const DOWNLOAD_FILE_PATH = '/api/acbackend/fileupservice/v1/download?path=aicode/';

//srdchat服务地址
export const ASK_QUESTION_PATH = '/api/smartassistbackend/ideChat/question';
export const RECORD_LIST_PATH = '/api/smartassistbackend/ideChat/recordList';

//prompts服务地址
export const TEMPLATES_PATH = '/api/acbackend/promptmgr/v1/templates';
export const GET_CATEGORIES_PATH = '/api/acbackend/promptmgr/v1/admin/categories';

//chathistory
export const LIST_DIALOGS_PATH = '/api/aebackend/chat-history-admin/v1/list-dialogs';
export const GET_DIALOGS_PATH = '/api/aebackend/chat-history-admin/v1/get-dialog';
export const EDIT_DIALOG_TITLE_PATH = '/api/aebackend/chat-history-admin/v1/edit-dialog-title';
export const REMOVE_DIALOG_PATH = '/api/aebackend/chat-history-admin/v1/remove-dialog';
export const FEEDBACK_ANSWER_PATH = '/api/aebackend/chat-history-admin/v1/feedback-answer';

//stopanswer
export const STOP_ANSWER_PATH = '/api/acbackend/code-chat/v1/stop-answer';

//knowledge base
export const GET_KB_INFO_PATH = '/api/smartassistbackend/aiknowledge-base/v1/ide/get-kb-info';
export const SEARCH_DEV_KBS_PATH = '/api/smartassistbackend/aiknowledge-base/v2/ide/search-dev-kbs';

//security scan
export const UPLOAD_SCAN_FILE_PATH = '/api/sqscapi/ide/upload';
export const QUERY_SCAN_ISSUES_PATH = '/api/sqscapi/ide/queryIssues';
export const STOP_SCAN_PATH = '/api/sqscapi/ide/stopTask';
export const AI_EXPLAIN_PATH = '/api/sqscapi/sqsc-ai-agent/multi-issue/explain'

// workItem
export const WORK_ITEM_PATH = '/api/agilebackend/workitem/srd-zte-workitems/simple-search';
