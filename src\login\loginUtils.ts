import * as fs from 'fs';
import * as os from 'os';
import * as vscode from 'vscode';
import axios from 'axios';
import { connect } from 'net';
import { GENERAL_IDENTIFIER, GENERAL_ADDRESS_SETTING } from '../settings/constants';
import { createDecipheriv } from 'crypto';
import { VscodeWorkbench, LoginStatus as srdLoginStatus } from '../common/constants';
import { SECIDEA_SETTINGS_ITEM } from '../common/config';
import fetch from 'node-fetch';

export const LOGIN_REDIRECT_URI = '/api/login-redirect';
export const SECIDEA_SETTINGS_COMMAND = 'secidea.settings';
export const loginStoreDir = `${process.env.HOME || process.env.USERPROFILE}/.secidea/plugin/store`;
export const loginStoreFileName = 'secidea_auth.json';
export const loginStoreFile = `${loginStoreDir}/${loginStoreFileName}`;
export const splitText = '&secidea&';

export enum LoginStatus {
  notLoggedIn = '未登录',
  loggedIn = '未授权',
  authorized = '已授权',
}

export class UserInfo {
  public userId = '';

  public userName = '';

  public authCode = '';

  public static equals(user1: UserInfo, user2: UserInfo): boolean {
    return (
      user1.userId === user2.userId &&
      user1.userName === user2.userName &&
      user1.authCode === user2.authCode
    );
  }
}

async function findAvailablePort(
  beginPort: number,
  endPort: number,
  host = '127.0.0.1'
): Promise<number | null> {
  // 参数验证
  if (beginPort > endPort) {
    throw new Error('beginPort 不能大于 endPort');
  }

  if (beginPort < 1 || endPort > 65535) {
    throw new Error('端口范围必须在 1-65535 之间');
  }

  // 遍历端口范围，找到第一个可用的端口
  for (let port = beginPort; port <= endPort; port++) {
    try {
      const isAvailable = await checkSinglePortAvailable(port, host);
      if (isAvailable) {
        return port;
      }
    } catch (error) {
      console.warn(`检查端口 ${port} 时出错:`, error);
    }
  }

  return null;
}

// 检查单个端口是否可用
function checkSinglePortAvailable(port: number, host = '127.0.0.1'): Promise<boolean> {
  return new Promise(resolve => {
    const socket = connect(port, host);

    socket.on('connect', () => {
      socket.destroy();
      resolve(false); // 端口被占用
    });

    socket.on('error', () => {
      resolve(true); // 端口可用
    });

    socket.setTimeout(1000, () => {
      socket.destroy();
      resolve(true); // 超时认为端口可用
    });
  });
}

class UserInfoManager {
  private static readonly authPluginName = ['all', 'c10'];

  private currentUserInfo: UserInfo = new UserInfo();

  private callbackPort: number | null = null;

  private address = '';

  private initLoginFinished = false;

  private constructor() {
    this.initLogin();
  }

  public static getInstance() {
    return new UserInfoManager();
  }

  public async getLoginCallbackPort(): Promise<number | null> {
    if (this.callbackPort === null) {
      const port = await findAvailablePort(61000, 61100);
      if (port === null) {
        vscode.window.showErrorMessage('当前机器在61000-61100之间无可用http端口，请检查机器');
      }
      this.callbackPort = port;
    }
    return this.callbackPort;
  }

  public initLogin() {
    try {
      if (!fs.existsSync(loginStoreDir)) {
        fs.mkdirSync(loginStoreDir, { recursive: true });
        const osName = os.platform().toLowerCase();
        if (osName.includes('darwin') || osName.includes('linux')) {
          fs.chmodSync(loginStoreDir, 0o777); // rwxrwxrwx
        }
      }

      if (fs.existsSync(loginStoreFile)) {
        try {
          const encryptedText = fs.readFileSync(loginStoreFile, 'utf-8');
          if (encryptedText) {
            const excyptedTexts = encryptedText.split(splitText);
            this.updateUserInfoAndAddress(decrypt(excyptedTexts[0]), excyptedTexts[1]);
          }
        } catch (e) {
          console.log('初始值解析异常忽略');
        }
      } else {
        this.store(splitText);
      }
      this.initLoginFinished = true;
    } catch (e) {
      console.error('无法创建授权文件目录', e);
      vscode.window.showErrorMessage('无法创建授权文件目录');
    }
  }

  public getLoginStatusForC10(): number {
    if (!this.initLoginFinished) {
      this.initLogin();
    }
    if (this.getLoginStatus() === LoginStatus.authorized) {
      return srdLoginStatus.OK;
    } else {
      return srdLoginStatus.NOT_OK;
    }
  }

  public login() {
    vscode.env
      .openExternal(vscode.Uri.parse(userInfoManager.getLoginUrl()))
      .then((success: boolean) => {
        if (!success) {
          vscode.window.showErrorMessage(
            '无法在浏览器打开登录页面，请确认服务器扫描地址是否填写正确，或联系客服人员'
          );
        }
      });
  }

  public logout(): void {
    const newAddress = vscode.workspace
      .getConfiguration(GENERAL_IDENTIFIER)
      .get(getConfigName(GENERAL_ADDRESS_SETTING)) as string;
    const encryptedText = fs.readFileSync(loginStoreFile, 'utf-8');
    this.address = newAddress;
    if (encryptedText !== `${splitText}${newAddress}`) {
      this.store(`${splitText}${newAddress}`);
    }
    vscode.commands.executeCommand(VscodeWorkbench.ACTION_OPEN_SETTINGS, SECIDEA_SETTINGS_ITEM);
  }

  public async handleCode(code: string): Promise<string> {
    const url = this.getUserInfoUrl(code);
    const response = await doGet(url);
    if (!response && this.getLoginStatus() === LoginStatus.notLoggedIn) {
      vscode.window.showErrorMessage('[海云安]登录后获取用户信息失败');
      return '';
    }
    this.store(`${response}${splitText}${this.address}`);
    return response;
  }

  public getLoginStatus(): string {
    if (!this.currentUserInfo.userName) {
      return LoginStatus.notLoggedIn;
    }

    return UserInfoManager.authPluginName.includes(this.currentUserInfo.authCode)
      ? LoginStatus.authorized
      : LoginStatus.loggedIn;
  }

  public isAuthorized(): boolean {
    return this.getLoginStatus() === LoginStatus.authorized;
  }

  public getUserId(): string {
    return this.currentUserInfo.userId;
  }

  public getAddress(): string {
    return this.address;
  }

  public getUserInfo(): UserInfo {
    return this.currentUserInfo;
  }

  public getLoginUrl(): string {
    const base64State = Buffer.from(this.getRedirectUrl()).toString('base64');
    const address = getAddress();
    return `${address}/scan/user/login?state=${base64State}&loginType=6&redirect_uri=${this.getSecideaServerUrl()}`;
  }

  public updateUserInfoAndAddress(userInfo: UserInfo, address: string, openSetting = false): void {
    if (UserInfo.equals(this.currentUserInfo, userInfo) && address === this.address) {
      return;
    }
    this.currentUserInfo = userInfo;
    this.address = address;
    const userStatus = this.getUser();
    const config = vscode.workspace.getConfiguration(GENERAL_IDENTIFIER);
    config.update('登录状态', userStatus, vscode.ConfigurationTarget.Global);
    config.update('服务器扫描地址', address, vscode.ConfigurationTarget.Global);
    if (openSetting) {
      vscode.commands.executeCommand(VscodeWorkbench.ACTION_OPEN_SETTINGS, SECIDEA_SETTINGS_ITEM);
    }
  }

  public store(content: string): void {
    fs.writeFileSync(loginStoreFile, content, 'utf-8');
  }

  private getUser(): string {
    const loginStatus = this.getLoginStatus();
    switch (loginStatus) {
      case LoginStatus.notLoggedIn:
        return loginStatus;
      case LoginStatus.loggedIn:
        return `${this.currentUserInfo.userName}(${loginStatus})`;
      default:
        return `${this.currentUserInfo.userName}(${loginStatus})`;
    }
  }

  private getUserInfoUrl(code: string): string {
    const address = getAddress();
    return `${address}/login/oauth/getUserInfo?code=${code}`;
  }

  private getRedirectUrl(): string {
    return `http://127.0.0.1:${this.callbackPort}${LOGIN_REDIRECT_URI}`;
  }

  private getSecideaServerUrl(): string {
    return `${getAddress()}/login/oauth/authorize/callback`;
  }
}

export const userInfoManager = UserInfoManager.getInstance();

export function decrypt(ciphertext: string): UserInfo {
  if (!ciphertext || ciphertext.trim() === '') {
    return new UserInfo();
  }

  const key = Buffer.from('xxxsecxxxideaxxx');
  const iv = Buffer.from('adci13cdgzxldcud');
  const decipher = createDecipheriv('aes-128-cbc', key, iv);
  let decrypted = decipher.update(Buffer.from(ciphertext, 'base64'));
  decrypted = Buffer.concat([decrypted, decipher.final()]);

  const decryptedString = decrypted.toString('utf-8');
  return JSON.parse(decryptedString) as UserInfo;
}

function getAddress(): string {
  return vscode.workspace
    .getConfiguration(GENERAL_IDENTIFIER)
    .get(getConfigName(GENERAL_ADDRESS_SETTING)) as string;
}

function getConfigName(setting: string): string {
  return setting.split(`.`)[1]!;
}

async function doGet(url: string): Promise<string> {
  let retryTime = 0;
  while (retryTime < 3) {
    try {
      const contentType = 'Content-Type';

      const response = await axios.get(url, {
        headers: {
          [contentType]: 'application/json',
        },
        timeout: 3000000,
      });
      console.log('[secidea] doGet: ' + response.data);
      return response.data;
    } catch (error) {
      console.log('[secidea] error: ' + error);
      retryTime += 1;
      console.log('[secidea] retryTime: ' + retryTime);
    }
  }
  return 'error';
}

// ------------测试网络连接------------
export async function testConnect(): Promise<boolean> {
  const url = getAddress();
  const queryUrl = url + '/sca-api/sysinfo/sdlSiteManager/queryOne';
  const result = await getPostResult(url);
  const result1 = await getPostResult(queryUrl);
  if (result || result1) {
    return true;
  }
  const result3 = await getPostResultUsingFetch(url);
  const result4 = await getPostResultUsingFetch(queryUrl);
  if (result3 || result4) {
    return true;
  }
  return false;
}

async function getPostResult(url: string): Promise<boolean> {
  try {
    const response = await axios.get(url);
    if (response.status === 200) {
      return true;
    }
  } catch (e: unknown) {
    console.error('[secidea] axios failed with error message: ' + (e as Error).message);
  }
  return false;
}
async function getPostResultUsingFetch(url: string): Promise<boolean> {
  try {
    const response = await fetch(url);
    if (response.status === 200) {
      return true;
    }
  } catch (e: unknown) {
    console.error('[secidea] fetch failed with error message: ' + (e as Error).message);
  }
  return false;
}
